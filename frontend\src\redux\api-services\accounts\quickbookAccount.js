import { api } from '../api';

// QuickBooks Account API endpoints using RTK Query
export const quickbookAccountApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all QuickBooks accounts
    getQuickbookAccounts: builder.query({
      query: (params = {}) => ({
        url: '/quickbooks/accounts',
        params: {
          organization_id: params.organization_id,
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          ...params,
        },
      }),
      providesTags: ['QuickBooks'],
      transformResponse: (response) => response.data,
    }),

    // Get QuickBooks account by ID
    getQuickbookAccountById: builder.query({
      query: (id) => `/quickbooks/accounts/${id}`,
      providesTags: (result, error, id) => [{ type: 'QuickBooks', id }],
      transformResponse: (response) => response.data,
    }),

    // Create/Add QuickBooks account
    addQuickbookAccount: builder.mutation({
      query: (accountData) => ({
        url: '/quickbooks/add',
        method: 'GET',
        params: accountData, // Using GET with query params as per existing implementation
      }),
      invalidatesTags: ['QuickBooks'],
      transformResponse: (response) => response.data,
    }),

    // Update QuickBooks account status
    updateQuickbookAccountStatus: builder.mutation({
      query: ({ id, status }) => ({
        url: `/quickbooks/status/${id}`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'QuickBooks', id },
        'QuickBooks',
      ],
      transformResponse: (response) => response.data,
    }),

    // Sync QuickBooks account
    syncQuickbookAccount: builder.mutation({
      query: ({ id, organization_id }) => ({
        url: `/quickbooks/accounts/${id}/sync`,
        method: 'POST',
        body: { organization_id },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'QuickBooks', id },
        'QuickBooks',
      ],
      transformResponse: (response) => response.data,
    }),

    // Delete QuickBooks account
    deleteQuickbookAccount: builder.mutation({
      query: (id) => ({
        url: `/quickbooks/accounts/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['QuickBooks'],
      transformResponse: (response) => response.data,
    }),

    // Get QuickBooks OAuth URL
    getQuickbookOAuthUrl: builder.query({
      query: () => '/quickbooks/oauth-url',
      transformResponse: (response) => response.data,
    }),

    // Get QuickBooks tokens
    getQuickbookTokens: builder.mutation({
      query: (tokenData) => ({
        url: '/quickbooks/token',
        method: 'POST',
        body: tokenData,
      }),
      transformResponse: (response) => response.data,
    }),

    // Add QuickBooks tokens
    addQuickbookTokens: builder.mutation({
      query: (tokenData) => ({
        url: '/quickbooks/add-token',
        method: 'POST',
        body: tokenData,
      }),
      invalidatesTags: ['QuickBooks'],
      transformResponse: (response) => response.data,
    }),

    // Save QuickBooks file
    saveQuickbookFile: builder.mutation({
      query: (fileData) => ({
        url: '/quickbooks',
        method: 'POST',
        body: fileData,
      }),
      transformResponse: (response) => response.data,
    }),

    // Get QuickBooks reports
    getQuickbookReports: builder.query({
      query: ({ accountId, reportType, ...params }) => ({
        url: `/quickbooks/accounts/${accountId}/reports/${reportType}`,
        params,
      }),
      providesTags: ['Reports'],
      transformResponse: (response) => response.data,
    }),

    // Fetch Trial Balance
    fetchQuickbookTrialBalance: builder.mutation({
      query: (reportData) => ({
        url: '/reports/trial-balance',
        method: 'POST',
        body: reportData,
      }),
      invalidatesTags: ['Reports'],
      transformResponse: (response) => response.data,
    }),

    // Fetch Profit & Loss
    fetchQuickbookProfitLoss: builder.mutation({
      query: (reportData) => ({
        url: '/reports/profit-loss',
        method: 'POST',
        body: reportData,
      }),
      invalidatesTags: ['Reports'],
      transformResponse: (response) => response.data,
    }),

    // Fetch Balance Sheet
    fetchQuickbookBalanceSheet: builder.mutation({
      query: (reportData) => ({
        url: '/reports/balance-sheet',
        method: 'POST',
        body: reportData,
      }),
      invalidatesTags: ['Reports'],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Account management hooks
  useGetQuickbookAccountsQuery,
  useGetQuickbookAccountByIdQuery,
  useAddQuickbookAccountMutation,
  useUpdateQuickbookAccountStatusMutation,
  useSyncQuickbookAccountMutation,
  useDeleteQuickbookAccountMutation,
  
  // OAuth and token hooks
  useGetQuickbookOAuthUrlQuery,
  useGetQuickbookTokensMutation,
  useAddQuickbookTokensMutation,
  
  // File operations
  useSaveQuickbookFileMutation,
  
  // Reports hooks
  useGetQuickbookReportsQuery,
  useFetchQuickbookTrialBalanceMutation,
  useFetchQuickbookProfitLossMutation,
  useFetchQuickbookBalanceSheetMutation,
} = quickbookAccountApi;
