import { createSlice } from "@reduxjs/toolkit";

/**
 * Common Redux utilities and helpers for consistent state management
 */

// Common initial state structure
export const createInitialState = (customState = {}) => ({
  data: null,
  items: [],
  selectedItem: null,
  loading: false,
  error: null,
  filters: {
    page: 1,
    pageSize: 10,
    search: "",
    sortBy: "id",
    sortOrder: "asc",
  },
  pagination: {
    total: 0,
    totalPages: 0,
    currentPage: 1,
    hasNext: false,
    hasPrev: false,
  },
  ...customState,
});

// Common reducer actions
export const createCommonReducers = () => ({
  // Loading states
  setLoading: (state, action) => {
    state.loading = action.payload;
  },
  clearLoading: (state) => {
    state.loading = false;
  },

  // Error handling
  setError: (state, action) => {
    state.error = action.payload;
    state.loading = false;
  },
  clearError: (state) => {
    state.error = null;
  },

  // Data management
  setData: (state, action) => {
    state.data = action.payload;
    state.loading = false;
    state.error = null;
  },
  clearData: (state) => {
    state.data = null;
    state.items = [];
    state.selectedItem = null;
  },

  // Items management
  setItems: (state, action) => {
    state.items = action.payload;
    state.loading = false;
    state.error = null;
  },
  addItem: (state, action) => {
    state.items.push(action.payload);
  },
  updateItem: (state, action) => {
    const { id, data } = action.payload;
    const index = state.items.findIndex((item) => item.id === id);
    if (index !== -1) {
      state.items[index] = { ...state.items[index], ...data };
    }
  },
  removeItem: (state, action) => {
    const id = action.payload;
    state.items = state.items.filter((item) => item.id !== id);
  },

  // Selection management
  setSelectedItem: (state, action) => {
    state.selectedItem = action.payload;
  },
  clearSelectedItem: (state) => {
    state.selectedItem = null;
  },

  // Filter management
  setFilters: (state, action) => {
    state.filters = { ...state.filters, ...action.payload };
  },
  resetFilters: (state) => {
    state.filters = {
      page: 1,
      pageSize: 10,
      search: "",
      sortBy: "id",
      sortOrder: "asc",
    };
  },

  // Pagination management
  setPagination: (state, action) => {
    state.pagination = { ...state.pagination, ...action.payload };
  },
  resetPagination: (state) => {
    state.pagination = {
      total: 0,
      totalPages: 0,
      currentPage: 1,
      hasNext: false,
      hasPrev: false,
    };
  },
});

// Async thunk state handlers
export const createAsyncHandlers = () => ({
  pending: (state) => {
    state.loading = true;
    state.error = null;
  },
  fulfilled: (state, action) => {
    state.loading = false;
    state.error = null;
    // Handle different response structures
    if (action.payload?.data) {
      state.data = action.payload.data;
      if (Array.isArray(action.payload.data)) {
        state.items = action.payload.data;
      }
    } else if (Array.isArray(action.payload)) {
      state.items = action.payload;
    } else {
      state.data = action.payload;
    }

    // Handle pagination if present
    if (action.payload?.pagination) {
      state.pagination = action.payload.pagination;
    }
  },
  rejected: (state, action) => {
    state.loading = false;
    state.error =
      action.payload || action.error?.message || "An error occurred";
  },
});

// RTK Query matcher handlers
export const createRTKQueryHandlers = () => ({
  fulfilled: (state, action) => {
    const data = action.payload?.data || action.payload;
    state.loading = false;
    state.error = null;

    if (Array.isArray(data)) {
      state.items = data;
    } else {
      state.data = data;
    }

    // Handle pagination
    if (action.payload?.pagination) {
      state.pagination = action.payload.pagination;
    }
  },
  rejected: (state, action) => {
    state.loading = false;
    state.error = action.payload?.message || "Failed to fetch data";
  },
});

// Create a standardized slice with common reducers
export const createStandardSlice = (
  name,
  initialState = {},
  customReducers = {}
) => {
  const slice = createSlice({
    name,
    initialState: createInitialState(initialState),
    reducers: {
      ...createCommonReducers(),
      ...customReducers,
    },
  });

  return slice;
};

// Helper to create async thunk extraReducers
export const createAsyncExtraReducers = (builder, asyncThunks) => {
  Object.entries(asyncThunks).forEach(([key, thunk]) => {
    const handlers = createAsyncHandlers();
    builder
      .addCase(thunk.pending, handlers.pending)
      .addCase(thunk.fulfilled, handlers.fulfilled)
      .addCase(thunk.rejected, handlers.rejected);
  });
};

// Helper to create RTK Query extraReducers
export const createRTKQueryExtraReducers = (builder, api, endpoints) => {
  endpoints.forEach((endpointName) => {
    const handlers = createRTKQueryHandlers();
    builder
      .addMatcher(
        api.endpoints[endpointName].matchFulfilled,
        handlers.fulfilled
      )
      .addMatcher(api.endpoints[endpointName].matchRejected, handlers.rejected);
  });
};

// Common selectors
export const createCommonSelectors = (selectSlice) => ({
  selectData: (state) => selectSlice(state).data,
  selectItems: (state) => selectSlice(state).items,
  selectSelectedItem: (state) => selectSlice(state).selectedItem,
  selectLoading: (state) => selectSlice(state).loading,
  selectError: (state) => selectSlice(state).error,
  selectFilters: (state) => selectSlice(state).filters,
  selectPagination: (state) => selectSlice(state).pagination,
});

// Error handling utilities
export const handleAsyncError = (error) => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return "An unexpected error occurred";
};

// Success message utilities
export const createSuccessMessage = (action, itemName) => {
  const actionMap = {
    create: `${itemName} created successfully`,
    update: `${itemName} updated successfully`,
    delete: `${itemName} deleted successfully`,
    fetch: `${itemName} loaded successfully`,
  };
  return actionMap[action] || `${action} completed successfully`;
};

export default {
  createInitialState,
  createCommonReducers,
  createAsyncHandlers,
  createRTKQueryHandlers,
  createStandardSlice,
  createAsyncExtraReducers,
  createRTKQueryExtraReducers,
  createCommonSelectors,
  handleAsyncError,
  createSuccessMessage,
};
