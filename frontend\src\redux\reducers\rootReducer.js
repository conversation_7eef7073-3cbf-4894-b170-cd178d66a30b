import { combineReducers } from "@reduxjs/toolkit";

// Import API reducer
import { api } from "../api-services/api";

// Import slice reducers
import authReducer from "../slices/authSlice";
import quickbooksAccountReducer from "../slices/quickbooksAccountSlice";
import sageAccountReducer from "../slices/sageAccountSlice";
import netsuiteAccountReducer from "../slices/netsuiteAccountSlice";
import mastersReducer from "../slices/mastersSlice";
import uiReducer from "../slices/uiSlice";

// Import existing reducers that need to be maintained
import organizationsReducer from "../../store/reducers/organizationsReducer";

// Combine all reducers
const rootReducer = combineReducers({
  // RTK Query API reducer
  [api.reducerPath]: api.reducer,

  // Auth slice
  auth: authReducer,

  // Account management slices
  quickbooksAccount: quickbooksAccountReducer,
  sageAccount: sageAccountReducer,
  netsuiteAccount: netsuiteAccountReducer,

  // Master data slice
  masters: mastersReducer,

  // UI state slice
  ui: uiReducer,

  // Legacy reducers (to be migrated later)
  organizations: organizationsReducer,
});

export default rootReducer;
