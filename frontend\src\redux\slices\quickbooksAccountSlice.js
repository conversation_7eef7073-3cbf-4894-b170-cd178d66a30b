import { createSlice } from "@reduxjs/toolkit";
import { quickbooksApi } from "../api-services/quickbooksApi";

const initialState = {
  quickbooksAccounts: [],
  selectedAccount: null,
  error: null,
  oauthUrl: null,
  lastSynced: null,
  syncStatus: {},
  filters: {
    organization_id: null,
    status: "all",
    search: "",
    page: 1,
    pageSize: 10,
  },
};

const quickbooksAccountSlice = createSlice({
  name: "quickbooksAccount",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearOAuthUrl: (state) => {
      state.oauthUrl = null;
    },
    setSelectedAccount: (state, action) => {
      state.selectedAccount = action.payload;
    },
    clearSelectedAccount: (state) => {
      state.selectedAccount = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },
    setSyncStatus: (state, action) => {
      const { accountId, status } = action.payload;
      state.syncStatus[accountId] = status;
    },
  },
  extraReducers: (builder) => {
    // Handle fetch accounts success
    builder.addMatcher(
      quickbooksApi.endpoints.getQuickbooksAccounts.matchFulfilled,
      (state, action) => {
        state.quickbooksAccounts = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle fetch accounts error
    builder.addMatcher(
      quickbooksApi.endpoints.getQuickbooksAccounts.matchRejected,
      (state, action) => {
        state.error =
          action.payload?.message || "Failed to fetch QuickBooks accounts";
      }
    );

    // Handle update status success
    builder.addMatcher(
      quickbooksApi.endpoints.updateQuickbooksAccountStatus.matchFulfilled,
      (state, action) => {
        const updatedAccount = action.payload?.data || action.payload;
        if (updatedAccount?.id) {
          state.quickbooksAccounts = state.quickbooksAccounts.map((account) =>
            account.id === updatedAccount.id
              ? { ...account, ...updatedAccount }
              : account
          );
        }
        state.error = null;
      }
    );

    // Handle update status error
    builder.addMatcher(
      quickbooksApi.endpoints.updateQuickbooksAccountStatus.matchRejected,
      (state, action) => {
        state.error =
          action.payload?.message || "Failed to update account status";
      }
    );

    // Handle sync account success
    builder.addMatcher(
      quickbooksApi.endpoints.syncQuickbooksAccount.matchFulfilled,
      (state, action) => {
        const syncedAccount = action.payload?.data || action.payload;
        if (syncedAccount?.id) {
          state.quickbooksAccounts = state.quickbooksAccounts.map((account) =>
            account.id === syncedAccount.id
              ? { ...account, ...syncedAccount }
              : account
          );
          state.lastSynced = new Date().toISOString();
          state.syncStatus[syncedAccount.id] = "completed";
        }
        state.error = null;
      }
    );

    // Handle sync account error
    builder.addMatcher(
      quickbooksApi.endpoints.syncQuickbooksAccount.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || "Failed to sync account";
      }
    );

    // Handle delete account success
    builder.addMatcher(
      quickbooksApi.endpoints.deleteQuickbooksAccount.matchFulfilled,
      (state, action) => {
        const deletedId =
          action.payload?.data?.id ||
          action.payload?.id ||
          action.meta?.arg?.originalArgs;
        if (deletedId) {
          state.quickbooksAccounts = state.quickbooksAccounts.filter(
            (account) => account.id !== deletedId
          );
          delete state.syncStatus[deletedId];
        }
        state.error = null;
      }
    );

    // Handle delete account error
    builder.addMatcher(
      quickbooksApi.endpoints.deleteQuickbooksAccount.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || "Failed to delete account";
      }
    );

    // Handle OAuth URL success
    builder.addMatcher(
      quickbooksApi.endpoints.getQuickbooksOAuthUrl.matchFulfilled,
      (state, action) => {
        state.oauthUrl =
          action.payload?.data?.oauth_url || action.payload?.oauth_url;
        state.error = null;
      }
    );

    // Handle OAuth URL error
    builder.addMatcher(
      quickbooksApi.endpoints.getQuickbooksOAuthUrl.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || "Failed to get OAuth URL";
      }
    );

    // Handle add account success
    builder.addMatcher(
      quickbooksApi.endpoints.addQuickbooksAccount.matchFulfilled,
      (state, action) => {
        const newAccount = action.payload?.data || action.payload;
        if (newAccount) {
          state.quickbooksAccounts.push(newAccount);
        }
        state.error = null;
      }
    );

    // Handle add account error
    builder.addMatcher(
      quickbooksApi.endpoints.addQuickbooksAccount.matchRejected,
      (state, action) => {
        state.error =
          action.payload?.message || "Failed to add QuickBooks account";
      }
    );
  },
});

export const {
  clearError,
  clearOAuthUrl,
  setSelectedAccount,
  clearSelectedAccount,
  setFilters,
  resetFilters,
  setSyncStatus,
} = quickbooksAccountSlice.actions;
export default quickbooksAccountSlice.reducer;
