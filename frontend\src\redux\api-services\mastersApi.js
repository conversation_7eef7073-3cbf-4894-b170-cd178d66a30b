import { api } from './api';

// Masters API endpoints using RTK Query
export const mastersApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all organizations
    getOrganizations: builder.query({
      query: (params = {}) => ({
        url: '/organization',
        params: {
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          ...params,
        },
      }),
      providesTags: ['Organization'],
      transformResponse: (response) => response.data,
    }),

    // Get organization by ID
    getOrganizationById: builder.query({
      query: (id) => `/organization/${id}`,
      providesTags: (result, error, id) => [{ type: 'Organization', id }],
      transformResponse: (response) => response.data,
    }),

    // Create organization
    createOrganization: builder.mutation({
      query: (organizationData) => ({
        url: '/organization',
        method: 'POST',
        body: organizationData,
      }),
      invalidatesTags: ['Organization'],
      transformResponse: (response) => response.data,
    }),

    // Update organization
    updateOrganization: builder.mutation({
      query: ({ id, ...organizationData }) => ({
        url: `/organization/${id}`,
        method: 'PUT',
        body: organizationData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Organization', id },
        'Organization',
      ],
      transformResponse: (response) => response.data,
    }),

    // Delete organization (soft delete)
    deleteOrganization: builder.mutation({
      query: (id) => ({
        url: `/organization/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Organization'],
      transformResponse: (response) => response.data,
    }),

    // Get all users
    getUsers: builder.query({
      query: (params = {}) => ({
        url: '/users',
        params: {
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          ...params,
        },
      }),
      providesTags: ['User'],
      transformResponse: (response) => response.data,
    }),

    // Get user by ID
    getUserById: builder.query({
      query: (id) => `/users/${id}`,
      providesTags: (result, error, id) => [{ type: 'User', id }],
      transformResponse: (response) => response.data,
    }),

    // Create user
    createUser: builder.mutation({
      query: (userData) => ({
        url: '/users',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: ['User'],
      transformResponse: (response) => response.data,
    }),

    // Update user
    updateUser: builder.mutation({
      query: ({ id, ...userData }) => ({
        url: `/users/${id}`,
        method: 'PUT',
        body: userData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'User', id },
        'User',
      ],
      transformResponse: (response) => response.data,
    }),

    // Delete user (soft delete)
    deleteUser: builder.mutation({
      query: (id) => ({
        url: `/users/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['User'],
      transformResponse: (response) => response.data,
    }),

    // Get all roles
    getRoles: builder.query({
      query: (params = {}) => ({
        url: '/roles',
        params,
      }),
      providesTags: ['Role'],
      transformResponse: (response) => response.data,
    }),

    // Get all permissions
    getPermissions: builder.query({
      query: (params = {}) => ({
        url: '/permissions',
        params,
      }),
      providesTags: ['Permission'],
      transformResponse: (response) => response.data,
    }),

    // Get master data (combined endpoint for dropdowns)
    getMasterData: builder.query({
      query: () => '/masters',
      providesTags: ['Masters'],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Organization hooks
  useGetOrganizationsQuery,
  useGetOrganizationByIdQuery,
  useCreateOrganizationMutation,
  useUpdateOrganizationMutation,
  useDeleteOrganizationMutation,
  
  // User hooks
  useGetUsersQuery,
  useGetUserByIdQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  
  // Role and Permission hooks
  useGetRolesQuery,
  useGetPermissionsQuery,
  
  // Master data hook
  useGetMasterDataQuery,
} = mastersApi;
