import { api } from "../api";

// NetSuite Account API endpoints using RTK Query
export const netsuiteAccountApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all NetSuite accounts
    getNetsuiteAccounts: builder.query({
      query: (params = {}) => ({
        url: "/netsuite/accounts",
        params: {
          organization_id: params.organization_id,
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          ...params,
        },
      }),
      providesTags: ["NetSuite"],
      transformResponse: (response) => response.data,
    }),

    // Get NetSuite account by ID
    getNetsuiteAccountById: builder.query({
      query: (id) => `/netsuite/accounts/${id}`,
      providesTags: (result, error, id) => [{ type: "NetSuite", id }],
      transformResponse: (response) => response.data,
    }),

    // Create NetSuite account
    createNetsuiteAccount: builder.mutation({
      query: (accountData) => ({
        url: "/netsuite/accounts",
        method: "POST",
        body: accountData,
      }),
      invalidatesTags: ["NetSuite"],
      transformResponse: (response) => response.data,
    }),

    // Update NetSuite account
    updateNetsuiteAccount: builder.mutation({
      query: ({ id, ...accountData }) => ({
        url: `/netsuite/accounts/${id}`,
        method: "PUT",
        body: accountData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "NetSuite", id },
        "NetSuite",
      ],
      transformResponse: (response) => response.data,
    }),

    // Update NetSuite account status
    updateNetsuiteAccountStatus: builder.mutation({
      query: ({ id, status }) => ({
        url: `/netsuite/accounts/${id}/status`,
        method: "PUT",
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "NetSuite", id },
        "NetSuite",
      ],
      transformResponse: (response) => response.data,
    }),

    // Sync NetSuite account
    syncNetsuiteAccount: builder.mutation({
      query: ({ id, organization_id }) => ({
        url: `/netsuite/accounts/${id}/sync`,
        method: "POST",
        body: { organization_id },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "NetSuite", id },
        "NetSuite",
      ],
      transformResponse: (response) => response.data,
    }),

    // Delete NetSuite account
    deleteNetsuiteAccount: builder.mutation({
      query: (id) => ({
        url: `/netsuite/accounts/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["NetSuite"],
      transformResponse: (response) => response.data,
    }),

    // Get NetSuite connection status
    getNetsuiteConnectionStatus: builder.query({
      query: (accountId) => `/netsuite/accounts/${accountId}/status`,
      providesTags: (result, error, accountId) => [
        { type: "NetSuite", id: `${accountId}-status` },
      ],
      transformResponse: (response) => response.data,
    }),

    // Test NetSuite connection
    testNetsuiteConnection: builder.mutation({
      query: (accountId) => ({
        url: `/netsuite/accounts/${accountId}/test-connection`,
        method: "POST",
      }),
      transformResponse: (response) => response.data,
    }),

    // Get NetSuite reports
    getNetsuiteReports: builder.query({
      query: ({ accountId, reportType, ...params }) => ({
        url: `/netsuite/accounts/${accountId}/reports/${reportType}`,
        params,
      }),
      providesTags: ["Reports"],
      transformResponse: (response) => response.data,
    }),

    // Fetch NetSuite data
    fetchNetsuiteData: builder.mutation({
      query: ({ accountId, dataType, ...params }) => ({
        url: `/netsuite/accounts/${accountId}/fetch/${dataType}`,
        method: "POST",
        body: params,
      }),
      invalidatesTags: ["NetSuite", "Reports"],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Account management hooks
  useGetNetsuiteAccountsQuery,
  useGetNetsuiteAccountByIdQuery,
  useCreateNetsuiteAccountMutation,
  useUpdateNetsuiteAccountMutation,
  useUpdateNetsuiteAccountStatusMutation,
  useSyncNetsuiteAccountMutation,
  useDeleteNetsuiteAccountMutation,

  // Connection hooks
  useGetNetsuiteConnectionStatusQuery,
  useTestNetsuiteConnectionMutation,

  // Reports and data hooks
  useGetNetsuiteReportsQuery,
  useFetchNetsuiteDataMutation,
} = netsuiteAccountApi;
