import { api } from '../api';

// Tenant API endpoints using RTK Query
export const tenantApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all tenants
    getTenants: builder.query({
      query: (params = {}) => ({
        url: '/tenants',
        params: {
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          ...params,
        },
      }),
      providesTags: ['Tenant'],
      transformResponse: (response) => response.data,
    }),

    // Get tenant by ID
    getTenantById: builder.query({
      query: (id) => `/tenants/${id}`,
      providesTags: (result, error, id) => [{ type: 'Tenant', id }],
      transformResponse: (response) => response.data,
    }),

    // Create tenant
    createTenant: builder.mutation({
      query: (tenantData) => ({
        url: '/tenants',
        method: 'POST',
        body: tenantData,
      }),
      invalidatesTags: ['Tenant'],
      transformResponse: (response) => response.data,
    }),

    // Update tenant
    updateTenant: builder.mutation({
      query: ({ id, ...tenantData }) => ({
        url: `/tenants/${id}`,
        method: 'PUT',
        body: tenantData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Tenant', id },
        'Tenant',
      ],
      transformResponse: (response) => response.data,
    }),

    // Delete tenant (soft delete)
    deleteTenant: builder.mutation({
      query: (id) => ({
        url: `/tenants/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Tenant'],
      transformResponse: (response) => response.data,
    }),

    // Get tenant settings
    getTenantSettings: builder.query({
      query: (tenantId) => `/tenants/${tenantId}/settings`,
      providesTags: (result, error, tenantId) => [{ type: 'Tenant', id: `${tenantId}-settings` }],
      transformResponse: (response) => response.data,
    }),

    // Update tenant settings
    updateTenantSettings: builder.mutation({
      query: ({ tenantId, settings }) => ({
        url: `/tenants/${tenantId}/settings`,
        method: 'PUT',
        body: settings,
      }),
      invalidatesTags: (result, error, { tenantId }) => [
        { type: 'Tenant', id: `${tenantId}-settings` },
        { type: 'Tenant', id: tenantId },
      ],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useGetTenantsQuery,
  useGetTenantByIdQuery,
  useCreateTenantMutation,
  useUpdateTenantMutation,
  useDeleteTenantMutation,
  useGetTenantSettingsQuery,
  useUpdateTenantSettingsMutation,
} = tenantApi;
