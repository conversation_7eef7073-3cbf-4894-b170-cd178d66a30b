import { createStandardSlice } from '../../../utils/methods/redux';
import { loginApi } from '../../api-services/auth/login';

const initialState = {
  isAuthenticated: false,
  token: localStorage.getItem('token') || null,
  loginAttempts: 0,
  lastLoginTime: null,
  rememberMe: false,
};

const loginSlice = createStandardSlice('login', initialState, {
  // Custom reducers specific to login
  setToken: (state, action) => {
    state.token = action.payload;
    if (action.payload) {
      localStorage.setItem('token', action.payload);
      state.isAuthenticated = true;
    } else {
      localStorage.removeItem('token');
      state.isAuthenticated = false;
    }
  },
  
  setAuthenticated: (state, action) => {
    state.isAuthenticated = action.payload;
  },
  
  incrementLoginAttempts: (state) => {
    state.loginAttempts += 1;
  },
  
  resetLoginAttempts: (state) => {
    state.loginAttempts = 0;
  },
  
  setLastLoginTime: (state, action) => {
    state.lastLoginTime = action.payload;
  },
  
  setRememberMe: (state, action) => {
    state.rememberMe = action.payload;
  },
  
  initializeAuth: (state) => {
    const token = localStorage.getItem('token');
    if (token) {
      state.token = token;
      state.isAuthenticated = true;
    }
  },
  
  clearAuth: (state) => {
    state.token = null;
    state.isAuthenticated = false;
    state.loginAttempts = 0;
    state.lastLoginTime = null;
    state.rememberMe = false;
    state.error = null;
    localStorage.removeItem('token');
  },
});

// Add extra reducers for RTK Query
loginSlice.caseReducers = {
  ...loginSlice.caseReducers,
  extraReducers: (builder) => {
    // Handle login success
    builder.addMatcher(
      loginApi.endpoints.login.matchFulfilled,
      (state, action) => {
        const { data } = action.payload;
        if (data?.token) {
          state.token = data.token;
          state.isAuthenticated = true;
          state.lastLoginTime = new Date().toISOString();
          state.loginAttempts = 0;
        }
        state.error = null;
      }
    );

    // Handle login error
    builder.addMatcher(
      loginApi.endpoints.login.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || 'Login failed';
        state.isAuthenticated = false;
        state.token = null;
        state.loginAttempts += 1;
      }
    );

    // Handle logout
    builder.addMatcher(
      loginApi.endpoints.logout.matchFulfilled,
      (state) => {
        state.token = null;
        state.isAuthenticated = false;
        state.loginAttempts = 0;
        state.lastLoginTime = null;
        state.error = null;
      }
    );

    // Handle token refresh
    builder.addMatcher(
      loginApi.endpoints.refreshToken.matchFulfilled,
      (state, action) => {
        const { data } = action.payload;
        if (data?.token) {
          state.token = data.token;
          state.isAuthenticated = true;
        }
        state.error = null;
      }
    );

    // Handle signup success
    builder.addMatcher(
      loginApi.endpoints.signUp.matchFulfilled,
      (state, action) => {
        // Optionally auto-login after signup
        const { data } = action.payload;
        if (data?.token) {
          state.token = data.token;
          state.isAuthenticated = true;
          state.lastLoginTime = new Date().toISOString();
        }
        state.error = null;
      }
    );
  },
};

export const {
  setToken,
  setAuthenticated,
  incrementLoginAttempts,
  resetLoginAttempts,
  setLastLoginTime,
  setRememberMe,
  initializeAuth,
  clearAuth,
  setLoading,
  setError,
  clearError,
} = loginSlice.actions;

export default loginSlice.reducer;
