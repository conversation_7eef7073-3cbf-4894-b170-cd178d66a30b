import { createStandardSlice } from '../../../utils/methods/redux';
import { organizationApi } from '../../api-services/masters/organization';

const initialState = {
  organizations: [],
  currentOrganization: null,
  organizationSettings: {},
  organizationUsers: {},
};

const organizationSlice = createStandardSlice('organization', initialState, {
  // Custom reducers specific to organization management
  setCurrentOrganization: (state, action) => {
    state.currentOrganization = action.payload;
  },
  
  clearCurrentOrganization: (state) => {
    state.currentOrganization = null;
  },
  
  setOrganizationSettings: (state, action) => {
    const { orgId, settings } = action.payload;
    state.organizationSettings[orgId] = settings;
  },
  
  setOrganizationUsers: (state, action) => {
    const { orgId, users } = action.payload;
    state.organizationUsers[orgId] = users;
  },
  
  updateOrganizationInList: (state, action) => {
    const { id, data } = action.payload;
    const index = state.organizations.findIndex(org => org.id === id);
    if (index !== -1) {
      state.organizations[index] = { ...state.organizations[index], ...data };
    }
  },
  
  addOrganizationToList: (state, action) => {
    state.organizations.push(action.payload);
  },
  
  removeOrganizationFromList: (state, action) => {
    const id = action.payload;
    state.organizations = state.organizations.filter(org => org.id !== id);
    // Clean up related data
    delete state.organizationSettings[id];
    delete state.organizationUsers[id];
  },
  
  clearOrganizationData: (state) => {
    state.organizations = [];
    state.currentOrganization = null;
    state.organizationSettings = {};
    state.organizationUsers = {};
    state.selectedItem = null;
    state.error = null;
  },
});

// Add extra reducers for RTK Query
organizationSlice.caseReducers = {
  ...organizationSlice.caseReducers,
  extraReducers: (builder) => {
    // Handle organizations fetch success
    builder.addMatcher(
      organizationApi.endpoints.getOrganizations.matchFulfilled,
      (state, action) => {
        state.organizations = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle organization fetch by ID success
    builder.addMatcher(
      organizationApi.endpoints.getOrganizationById.matchFulfilled,
      (state, action) => {
        state.selectedItem = action.payload;
        state.error = null;
      }
    );

    // Handle organization create success
    builder.addMatcher(
      organizationApi.endpoints.createOrganization.matchFulfilled,
      (state, action) => {
        state.organizations.push(action.payload);
        state.error = null;
      }
    );

    // Handle organization update success
    builder.addMatcher(
      organizationApi.endpoints.updateOrganization.matchFulfilled,
      (state, action) => {
        const updatedOrg = action.payload;
        const index = state.organizations.findIndex(org => org.id === updatedOrg.id);
        if (index !== -1) {
          state.organizations[index] = updatedOrg;
        }
        if (state.currentOrganization?.id === updatedOrg.id) {
          state.currentOrganization = updatedOrg;
        }
        state.error = null;
      }
    );

    // Handle organization delete success
    builder.addMatcher(
      organizationApi.endpoints.deleteOrganization.matchFulfilled,
      (state, action) => {
        const deletedId = action.meta.arg.originalArgs || action.meta.arg;
        state.organizations = state.organizations.filter(org => org.id !== deletedId);
        if (state.currentOrganization?.id === deletedId) {
          state.currentOrganization = null;
        }
        delete state.organizationSettings[deletedId];
        delete state.organizationUsers[deletedId];
        state.error = null;
      }
    );

    // Handle organization settings fetch success
    builder.addMatcher(
      organizationApi.endpoints.getOrganizationSettings.matchFulfilled,
      (state, action) => {
        const orgId = action.meta.arg.originalArgs || action.meta.arg;
        state.organizationSettings[orgId] = action.payload;
        state.error = null;
      }
    );

    // Handle organization settings update success
    builder.addMatcher(
      organizationApi.endpoints.updateOrganizationSettings.matchFulfilled,
      (state, action) => {
        const { orgId } = action.meta.arg.originalArgs || action.meta.arg;
        state.organizationSettings[orgId] = { ...state.organizationSettings[orgId], ...action.payload };
        state.error = null;
      }
    );

    // Handle organization users fetch success
    builder.addMatcher(
      organizationApi.endpoints.getOrganizationUsers.matchFulfilled,
      (state, action) => {
        const orgId = action.meta.arg.originalArgs || action.meta.arg;
        state.organizationUsers[orgId] = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle any organization API errors
    builder.addMatcher(
      (action) => action.type.endsWith('/rejected') && action.type.includes('organization'),
      (state, action) => {
        state.error = action.payload?.message || 'Organization operation failed';
      }
    );
  },
};

export const {
  setCurrentOrganization,
  clearCurrentOrganization,
  setOrganizationSettings,
  setOrganizationUsers,
  updateOrganizationInList,
  addOrganizationToList,
  removeOrganizationFromList,
  clearOrganizationData,
  setLoading,
  setError,
  clearError,
  setItems,
  setSelectedItem,
  clearSelectedItem,
  setFilters,
  resetFilters,
  setPagination,
} = organizationSlice.actions;

export default organizationSlice.reducer;
