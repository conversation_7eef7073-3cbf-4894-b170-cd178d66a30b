import { createSlice } from '@reduxjs/toolkit';
import { mastersApi } from '../api-services/mastersApi';

const initialState = {
  // Selected data for forms and UI
  selectedOrganization: null,
  selectedUser: null,
  
  // Cached master data for dropdowns
  organizations: [],
  users: [],
  roles: [],
  permissions: [],
  masterData: null,
  
  // UI state
  filters: {
    organization: {
      page: 1,
      pageSize: 10,
      search: '',
      sortBy: 'name',
      sortOrder: 'asc'
    },
    user: {
      page: 1,
      pageSize: 10,
      search: '',
      sortBy: 'name',
      sortOrder: 'asc'
    }
  },
  
  // Error handling
  error: null
};

const mastersSlice = createSlice({
  name: 'masters',
  initialState,
  reducers: {
    // Selection actions
    setSelectedOrganization: (state, action) => {
      state.selectedOrganization = action.payload;
    },
    setSelectedUser: (state, action) => {
      state.selectedUser = action.payload;
    },
    clearSelections: (state) => {
      state.selectedOrganization = null;
      state.selectedUser = null;
    },
    
    // Filter actions
    setOrganizationFilters: (state, action) => {
      state.filters.organization = { ...state.filters.organization, ...action.payload };
    },
    setUserFilters: (state, action) => {
      state.filters.user = { ...state.filters.user, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // Error handling
    clearError: (state) => {
      state.error = null;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    
    // Cache management
    clearCache: (state) => {
      state.organizations = [];
      state.users = [];
      state.roles = [];
      state.permissions = [];
      state.masterData = null;
    }
  },
  extraReducers: (builder) => {
    // Handle organizations fetch success
    builder.addMatcher(
      mastersApi.endpoints.getOrganizations.matchFulfilled,
      (state, action) => {
        state.organizations = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle organizations fetch error
    builder.addMatcher(
      mastersApi.endpoints.getOrganizations.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || 'Failed to fetch organizations';
      }
    );

    // Handle users fetch success
    builder.addMatcher(
      mastersApi.endpoints.getUsers.matchFulfilled,
      (state, action) => {
        state.users = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle users fetch error
    builder.addMatcher(
      mastersApi.endpoints.getUsers.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || 'Failed to fetch users';
      }
    );

    // Handle roles fetch success
    builder.addMatcher(
      mastersApi.endpoints.getRoles.matchFulfilled,
      (state, action) => {
        state.roles = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle permissions fetch success
    builder.addMatcher(
      mastersApi.endpoints.getPermissions.matchFulfilled,
      (state, action) => {
        state.permissions = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle master data fetch success
    builder.addMatcher(
      mastersApi.endpoints.getMasterData.matchFulfilled,
      (state, action) => {
        state.masterData = action.payload;
        // Update individual arrays if they exist in master data
        if (action.payload?.organizations) {
          state.organizations = action.payload.organizations;
        }
        if (action.payload?.users) {
          state.users = action.payload.users;
        }
        if (action.payload?.roles) {
          state.roles = action.payload.roles;
        }
        if (action.payload?.permissions) {
          state.permissions = action.payload.permissions;
        }
        state.error = null;
      }
    );

    // Handle create/update/delete success - invalidate cache
    builder.addMatcher(
      (action) => {
        return [
          mastersApi.endpoints.createOrganization.matchFulfilled,
          mastersApi.endpoints.updateOrganization.matchFulfilled,
          mastersApi.endpoints.deleteOrganization.matchFulfilled,
          mastersApi.endpoints.createUser.matchFulfilled,
          mastersApi.endpoints.updateUser.matchFulfilled,
          mastersApi.endpoints.deleteUser.matchFulfilled,
        ].some(matcher => matcher(action));
      },
      (state) => {
        // Clear error on successful operations
        state.error = null;
      }
    );

    // Handle mutation errors
    builder.addMatcher(
      (action) => {
        return action.type.endsWith('/rejected') && 
               action.type.includes('masters') &&
               (action.type.includes('create') || 
                action.type.includes('update') || 
                action.type.includes('delete'));
      },
      (state, action) => {
        state.error = action.payload?.message || 'Operation failed';
      }
    );
  }
});

export const {
  setSelectedOrganization,
  setSelectedUser,
  clearSelections,
  setOrganizationFilters,
  setUserFilters,
  resetFilters,
  clearError,
  setError,
  clearCache
} = mastersSlice.actions;

export default mastersSlice.reducer;
