import { createAsyncThunk } from '@reduxjs/toolkit';
import { handleAsyncError } from '../../../utils/methods/redux';
import api from '../../../lib/axios';

// Login thunk for complex login flows
export const performLogin = createAsyncThunk(
  'auth/performLogin',
  async ({ credentials, rememberMe = false }, { rejectWithValue, dispatch }) => {
    try {
      const response = await api.post('/auth/login', credentials);
      
      // Store token based on remember me preference
      if (rememberMe) {
        localStorage.setItem('token', response.data.data.token);
        localStorage.setItem('rememberMe', 'true');
      } else {
        sessionStorage.setItem('token', response.data.data.token);
        localStorage.removeItem('rememberMe');
      }
      
      // Log successful login
      console.log('Login successful:', response.data);
      
      return {
        ...response.data,
        rememberMe,
      };
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Auto login thunk for checking existing sessions
export const autoLogin = createAsyncThunk(
  'auth/autoLogin',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      // Check for existing token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const rememberMe = localStorage.getItem('rememberMe') === 'true';
      
      if (!token) {
        throw new Error('No token found');
      }
      
      // Verify token with server
      const response = await api.get('/auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      return {
        user: response.data.data,
        token,
        rememberMe,
      };
    } catch (error) {
      // Clear invalid tokens
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      localStorage.removeItem('rememberMe');
      
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Logout thunk for complex logout flows
export const performLogout = createAsyncThunk(
  'auth/performLogout',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      // Call logout endpoint
      await api.post('/auth/logout');
      
      // Clear all stored data
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      localStorage.removeItem('rememberMe');
      localStorage.removeItem('user');
      
      // Clear any other auth-related data
      localStorage.removeItem('lastLoginTime');
      
      return { success: true };
    } catch (error) {
      // Even if server logout fails, clear local data
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      localStorage.removeItem('rememberMe');
      localStorage.removeItem('user');
      
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Token refresh thunk
export const refreshAuthToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue, getState }) => {
    try {
      const state = getState();
      const currentToken = state.auth.login.token;
      
      if (!currentToken) {
        throw new Error('No token to refresh');
      }
      
      const response = await api.post('/auth/refresh-token', {}, {
        headers: { Authorization: `Bearer ${currentToken}` }
      });
      
      const newToken = response.data.data.token;
      
      // Update token in storage
      const rememberMe = localStorage.getItem('rememberMe') === 'true';
      if (rememberMe) {
        localStorage.setItem('token', newToken);
      } else {
        sessionStorage.setItem('token', newToken);
      }
      
      return {
        token: newToken,
        user: response.data.data.user,
      };
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Social login thunk
export const socialLogin = createAsyncThunk(
  'auth/socialLogin',
  async ({ provider, code, state }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/auth/social/${provider}`, {
        code,
        state,
      });
      
      // Store token
      localStorage.setItem('token', response.data.data.token);
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Two-factor authentication thunk
export const verifyTwoFactor = createAsyncThunk(
  'auth/verifyTwoFactor',
  async ({ token, code }, { rejectWithValue }) => {
    try {
      const response = await api.post('/auth/verify-2fa', {
        token,
        code,
      });
      
      // Store final token after 2FA verification
      localStorage.setItem('token', response.data.data.token);
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

export default {
  performLogin,
  autoLogin,
  performLogout,
  refreshAuthToken,
  socialLogin,
  verifyTwoFactor,
};
