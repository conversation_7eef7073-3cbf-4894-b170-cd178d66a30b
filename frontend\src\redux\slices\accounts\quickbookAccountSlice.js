import { createStandardSlice } from '../../../utils/methods/redux';
import { quickbookAccountApi } from '../../api-services/accounts/quickbookAccount';

const initialState = {
  accounts: [],
  oauthUrl: null,
  lastSynced: null,
  syncStatus: {},
  reports: {},
};

const quickbookAccountSlice = createStandardSlice('quickbookAccount', initialState, {
  // Custom reducers specific to QuickBooks account management
  setOAuthUrl: (state, action) => {
    state.oauthUrl = action.payload;
  },
  
  clearOAuthUrl: (state) => {
    state.oauthUrl = null;
  },
  
  setSyncStatus: (state, action) => {
    const { accountId, status } = action.payload;
    state.syncStatus[accountId] = status;
  },
  
  setLastSynced: (state, action) => {
    state.lastSynced = action.payload;
  },
  
  setReports: (state, action) => {
    const { accountId, reportType, data } = action.payload;
    if (!state.reports[accountId]) {
      state.reports[accountId] = {};
    }
    state.reports[accountId][reportType] = data;
  },
  
  updateAccountInList: (state, action) => {
    const { id, data } = action.payload;
    const index = state.accounts.findIndex(account => account.id === id);
    if (index !== -1) {
      state.accounts[index] = { ...state.accounts[index], ...data };
    }
  },
  
  addAccountToList: (state, action) => {
    state.accounts.push(action.payload);
  },
  
  removeAccountFromList: (state, action) => {
    const id = action.payload;
    state.accounts = state.accounts.filter(account => account.id !== id);
    delete state.syncStatus[id];
    delete state.reports[id];
  },
  
  clearAccountData: (state) => {
    state.accounts = [];
    state.oauthUrl = null;
    state.lastSynced = null;
    state.syncStatus = {};
    state.reports = {};
    state.selectedItem = null;
    state.error = null;
  },
});

// Add extra reducers for RTK Query
quickbookAccountSlice.caseReducers = {
  ...quickbookAccountSlice.caseReducers,
  extraReducers: (builder) => {
    // Handle accounts fetch success
    builder.addMatcher(
      quickbookAccountApi.endpoints.getQuickbookAccounts.matchFulfilled,
      (state, action) => {
        state.accounts = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle account fetch by ID success
    builder.addMatcher(
      quickbookAccountApi.endpoints.getQuickbookAccountById.matchFulfilled,
      (state, action) => {
        state.selectedItem = action.payload;
        state.error = null;
      }
    );

    // Handle account add success
    builder.addMatcher(
      quickbookAccountApi.endpoints.addQuickbookAccount.matchFulfilled,
      (state, action) => {
        const newAccount = action.payload;
        if (newAccount) {
          state.accounts.push(newAccount);
        }
        state.error = null;
      }
    );

    // Handle account status update success
    builder.addMatcher(
      quickbookAccountApi.endpoints.updateQuickbookAccountStatus.matchFulfilled,
      (state, action) => {
        const updatedAccount = action.payload;
        const index = state.accounts.findIndex(account => account.id === updatedAccount.id);
        if (index !== -1) {
          state.accounts[index] = { ...state.accounts[index], ...updatedAccount };
        }
        state.error = null;
      }
    );

    // Handle account sync success
    builder.addMatcher(
      quickbookAccountApi.endpoints.syncQuickbookAccount.matchFulfilled,
      (state, action) => {
        const syncedAccount = action.payload;
        const index = state.accounts.findIndex(account => account.id === syncedAccount.id);
        if (index !== -1) {
          state.accounts[index] = { ...state.accounts[index], ...syncedAccount };
        }
        state.lastSynced = new Date().toISOString();
        state.syncStatus[syncedAccount.id] = 'completed';
        state.error = null;
      }
    );

    // Handle account delete success
    builder.addMatcher(
      quickbookAccountApi.endpoints.deleteQuickbookAccount.matchFulfilled,
      (state, action) => {
        const deletedId = action.meta.arg.originalArgs || action.meta.arg;
        state.accounts = state.accounts.filter(account => account.id !== deletedId);
        delete state.syncStatus[deletedId];
        delete state.reports[deletedId];
        state.error = null;
      }
    );

    // Handle OAuth URL fetch success
    builder.addMatcher(
      quickbookAccountApi.endpoints.getQuickbookOAuthUrl.matchFulfilled,
      (state, action) => {
        state.oauthUrl = action.payload?.oauth_url || action.payload?.data?.oauth_url;
        state.error = null;
      }
    );

    // Handle reports fetch success
    builder.addMatcher(
      quickbookAccountApi.endpoints.getQuickbookReports.matchFulfilled,
      (state, action) => {
        const { accountId, reportType } = action.meta.arg.originalArgs || action.meta.arg;
        if (!state.reports[accountId]) {
          state.reports[accountId] = {};
        }
        state.reports[accountId][reportType] = action.payload;
        state.error = null;
      }
    );

    // Handle trial balance fetch success
    builder.addMatcher(
      quickbookAccountApi.endpoints.fetchQuickbookTrialBalance.matchFulfilled,
      (state, action) => {
        // Store trial balance data
        state.data = action.payload;
        state.error = null;
      }
    );

    // Handle profit loss fetch success
    builder.addMatcher(
      quickbookAccountApi.endpoints.fetchQuickbookProfitLoss.matchFulfilled,
      (state, action) => {
        // Store profit loss data
        state.data = action.payload;
        state.error = null;
      }
    );

    // Handle balance sheet fetch success
    builder.addMatcher(
      quickbookAccountApi.endpoints.fetchQuickbookBalanceSheet.matchFulfilled,
      (state, action) => {
        // Store balance sheet data
        state.data = action.payload;
        state.error = null;
      }
    );

    // Handle any QuickBooks API errors
    builder.addMatcher(
      (action) => action.type.endsWith('/rejected') && action.type.includes('quickbook'),
      (state, action) => {
        state.error = action.payload?.message || 'QuickBooks operation failed';
      }
    );
  },
};

export const {
  setOAuthUrl,
  clearOAuthUrl,
  setSyncStatus,
  setLastSynced,
  setReports,
  updateAccountInList,
  addAccountToList,
  removeAccountFromList,
  clearAccountData,
  setLoading,
  setError,
  clearError,
  setItems,
  setSelectedItem,
  clearSelectedItem,
  setFilters,
  resetFilters,
  setPagination,
} = quickbookAccountSlice.actions;

export default quickbookAccountSlice.reducer;
