import { api } from '../api';

// Password management API endpoints using RTK Query
export const passwordApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Forgot password
    forgotPassword: builder.mutation({
      query: ({ email, url }) => ({
        url: '/auth/forgot-password',
        method: 'POST',
        body: { email, url },
      }),
      transformResponse: (response) => response.data,
    }),

    // Reset password
    resetPassword: builder.mutation({
      query: ({ token, newPassword, confirmPassword }) => ({
        url: '/auth/reset-password',
        method: 'POST',
        body: { token, newPassword, confirmPassword },
      }),
      transformResponse: (response) => response.data,
    }),

    // Change password
    changePassword: builder.mutation({
      query: ({ oldPassword, newPassword, confirmPassword }) => ({
        url: '/auth/change-password',
        method: 'PUT',
        body: { oldPassword, newPassword, confirmPassword },
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useChangePasswordMutation,
} = passwordApi;
