import { api } from './api';

// Auth API endpoints using RTK Query
export const authApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Login user
    login: builder.mutation({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response) => {
        // Store token in localStorage for persistence
        if (response.data?.token) {
          localStorage.setItem('token', response.data.token);
        }
        return response;
      },
    }),

    // Get user profile
    getProfile: builder.query({
      query: () => '/auth/me',
      providesTags: ['Auth'],
      transformResponse: (response) => response.data,
    }),

    // Update user profile
    updateProfile: builder.mutation({
      query: ({ userId, userData }) => ({
        url: `/auth/profile/${userId}`,
        method: 'PUT',
        body: userData,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response) => response.data,
    }),

    // Logout user
    logout: builder.mutation({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['Auth'],
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
          // Clear token from localStorage
          localStorage.removeItem('token');
          // Clear auth state
          dispatch(api.util.resetApiState());
        } catch (error) {
          // Even if logout fails on server, clear local data
          localStorage.removeItem('token');
          dispatch(api.util.resetApiState());
        }
      },
    }),

    // Refresh token
    refreshToken: builder.mutation({
      query: () => ({
        url: '/auth/refresh-token',
        method: 'POST',
      }),
      transformResponse: (response) => {
        // Update token in localStorage
        if (response.data?.token) {
          localStorage.setItem('token', response.data.token);
        }
        return response;
      },
    }),

    // Forgot password
    forgotPassword: builder.mutation({
      query: ({ email, url }) => ({
        url: '/auth/forgot-password',
        method: 'POST',
        body: { email, url },
      }),
      transformResponse: (response) => response.data,
    }),

    // Reset password
    resetPassword: builder.mutation({
      query: ({ token, newPassword, confirmPassword }) => ({
        url: '/auth/reset-password',
        method: 'POST',
        body: { token, newPassword, confirmPassword },
      }),
      transformResponse: (response) => response.data,
    }),

    // Change password
    changePassword: builder.mutation({
      query: ({ oldPassword, newPassword, confirmPassword }) => ({
        url: '/auth/change-password',
        method: 'PUT',
        body: { oldPassword, newPassword, confirmPassword },
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response) => response.data,
    }),

    // Sign up user
    signUp: builder.mutation({
      query: (userData) => ({
        url: '/auth/signup',
        method: 'POST',
        body: userData,
      }),
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useLoginMutation,
  useGetProfileQuery,
  useUpdateProfileMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useChangePasswordMutation,
  useSignUpMutation,
} = authApi;
