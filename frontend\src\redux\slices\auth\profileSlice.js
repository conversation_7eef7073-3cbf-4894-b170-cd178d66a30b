import { createStandardSlice } from '../../../utils/methods/redux';
import { profileApi } from '../../api-services/auth/profile';

const initialState = {
  user: null,
  preferences: {},
  avatar: null,
  lastUpdated: null,
};

const profileSlice = createStandardSlice('profile', initialState, {
  // Custom reducers specific to profile
  setUser: (state, action) => {
    state.user = action.payload;
    state.lastUpdated = new Date().toISOString();
  },
  
  updateUserField: (state, action) => {
    const { field, value } = action.payload;
    if (state.user) {
      state.user[field] = value;
      state.lastUpdated = new Date().toISOString();
    }
  },
  
  setPreferences: (state, action) => {
    state.preferences = { ...state.preferences, ...action.payload };
  },
  
  setAvatar: (state, action) => {
    state.avatar = action.payload;
  },
  
  clearProfile: (state) => {
    state.user = null;
    state.preferences = {};
    state.avatar = null;
    state.lastUpdated = null;
    state.error = null;
  },
});

// Add extra reducers for RTK Query
profileSlice.caseReducers = {
  ...profileSlice.caseReducers,
  extraReducers: (builder) => {
    // Handle profile fetch success
    builder.addMatcher(
      profileApi.endpoints.getProfile.matchFulfilled,
      (state, action) => {
        state.user = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      }
    );

    // Handle profile fetch error
    builder.addMatcher(
      profileApi.endpoints.getProfile.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || 'Failed to fetch profile';
      }
    );

    // Handle profile update success
    builder.addMatcher(
      profileApi.endpoints.updateProfile.matchFulfilled,
      (state, action) => {
        state.user = { ...state.user, ...action.payload };
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      }
    );

    // Handle profile update error
    builder.addMatcher(
      profileApi.endpoints.updateProfile.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || 'Failed to update profile';
      }
    );
  },
};

export const {
  setUser,
  updateUserField,
  setPreferences,
  setAvatar,
  clearProfile,
  setLoading,
  setError,
  clearError,
  setData,
  clearData,
} = profileSlice.actions;

export default profileSlice.reducer;
