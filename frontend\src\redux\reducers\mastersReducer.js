import { combineReducers } from '@reduxjs/toolkit';

// Import masters sub-slices
import tenantReducer from '../slices/masters/tenantSlice';
import userReducer from '../slices/masters/userSlice';
import organizationReducer from '../slices/masters/organizationSlice';

// Import masters thunks for extraReducers
import tenantThunks from '../thunks/masters/tenantThunk';

// Combine all masters-related reducers
const mastersReducer = combineReducers({
  tenant: tenantReducer,
  user: userReducer,
  organization: organizationReducer,
});

// Create a wrapper to add extra reducers for thunks
const mastersReducerWithThunks = (state, action) => {
  // First, let the combined reducer handle the action
  const newState = mastersReducer(state, action);
  
  // Then handle thunk actions that affect multiple sub-slices
  switch (action.type) {
    // Handle bulk tenant operations success
    case tenantThunks.bulkTenantOperations.fulfilled.type:
      const { operation, results } = action.payload;
      let updatedTenants = [...newState.tenant.items];
      
      if (operation === 'delete') {
        const deletedIds = results.map(r => r.id);
        updatedTenants = updatedTenants.filter(tenant => !deletedIds.includes(tenant.id));
      } else if (operation === 'update') {
        results.forEach(result => {
          const index = updatedTenants.findIndex(tenant => tenant.id === result.id);
          if (index !== -1) {
            updatedTenants[index] = { ...updatedTenants[index], ...result };
          }
        });
      }
      
      return {
        ...newState,
        tenant: {
          ...newState.tenant,
          items: updatedTenants,
          error: null,
        },
      };
    
    // Handle tenant import success
    case tenantThunks.importTenants.fulfilled.type:
      return {
        ...newState,
        tenant: {
          ...newState.tenant,
          items: [...newState.tenant.items, ...action.payload.imported],
          error: null,
        },
      };
    
    // Handle tenant duplicate success
    case tenantThunks.duplicateTenant.fulfilled.type:
      return {
        ...newState,
        tenant: {
          ...newState.tenant,
          items: [...newState.tenant.items, action.payload],
          error: null,
        },
      };
    
    // Handle tenant archive success
    case tenantThunks.archiveTenant.fulfilled.type:
      const archivedTenant = action.payload;
      const archivedIndex = newState.tenant.items.findIndex(tenant => tenant.id === archivedTenant.id);
      let archivedTenants = [...newState.tenant.items];
      
      if (archivedIndex !== -1) {
        archivedTenants[archivedIndex] = archivedTenant;
      }
      
      return {
        ...newState,
        tenant: {
          ...newState.tenant,
          items: archivedTenants,
          error: null,
        },
      };
    
    // Handle tenant restore success
    case tenantThunks.restoreTenant.fulfilled.type:
      const restoredTenant = action.payload;
      const restoredIndex = newState.tenant.items.findIndex(tenant => tenant.id === restoredTenant.id);
      let restoredTenants = [...newState.tenant.items];
      
      if (restoredIndex !== -1) {
        restoredTenants[restoredIndex] = restoredTenant;
      } else {
        restoredTenants.push(restoredTenant);
      }
      
      return {
        ...newState,
        tenant: {
          ...newState.tenant,
          items: restoredTenants,
          error: null,
        },
      };
    
    // Handle tenant sync with external system success
    case tenantThunks.syncTenantWithExternal.fulfilled.type:
      const syncedTenant = action.payload;
      const syncedIndex = newState.tenant.items.findIndex(tenant => tenant.id === syncedTenant.id);
      let syncedTenants = [...newState.tenant.items];
      
      if (syncedIndex !== -1) {
        syncedTenants[syncedIndex] = { ...syncedTenants[syncedIndex], ...syncedTenant };
      }
      
      return {
        ...newState,
        tenant: {
          ...newState.tenant,
          items: syncedTenants,
          error: null,
        },
      };
    
    // Handle masters errors
    case tenantThunks.bulkTenantOperations.rejected.type:
    case tenantThunks.importTenants.rejected.type:
    case tenantThunks.duplicateTenant.rejected.type:
    case tenantThunks.archiveTenant.rejected.type:
    case tenantThunks.restoreTenant.rejected.type:
    case tenantThunks.syncTenantWithExternal.rejected.type:
      return {
        ...newState,
        tenant: {
          ...newState.tenant,
          error: action.payload,
          loading: false,
        },
      };
    
    default:
      return newState;
  }
};

// Export selectors for the masters module
export const mastersSelectors = {
  // Tenant selectors
  selectTenants: (state) => state.masters.tenant.items,
  selectCurrentTenant: (state) => state.masters.tenant.currentTenant,
  selectTenantSettings: (state) => state.masters.tenant.tenantSettings,
  selectSelectedTenant: (state) => state.masters.tenant.selectedItem,
  selectTenantLoading: (state) => state.masters.tenant.loading,
  selectTenantError: (state) => state.masters.tenant.error,
  selectTenantFilters: (state) => state.masters.tenant.filters,
  selectTenantPagination: (state) => state.masters.tenant.pagination,
  
  // User selectors
  selectUsers: (state) => state.masters.user.items,
  selectUserPermissions: (state) => state.masters.user.userPermissions,
  selectUsersByRole: (state) => state.masters.user.usersByRole,
  selectUsersByOrganization: (state) => state.masters.user.usersByOrganization,
  selectSelectedUser: (state) => state.masters.user.selectedItem,
  selectUserLoading: (state) => state.masters.user.loading,
  selectUserError: (state) => state.masters.user.error,
  selectUserFilters: (state) => state.masters.user.filters,
  selectUserPagination: (state) => state.masters.user.pagination,
  
  // Organization selectors
  selectOrganizations: (state) => state.masters.organization.items,
  selectCurrentOrganization: (state) => state.masters.organization.currentOrganization,
  selectOrganizationSettings: (state) => state.masters.organization.organizationSettings,
  selectOrganizationUsers: (state) => state.masters.organization.organizationUsers,
  selectSelectedOrganization: (state) => state.masters.organization.selectedItem,
  selectOrganizationLoading: (state) => state.masters.organization.loading,
  selectOrganizationError: (state) => state.masters.organization.error,
  selectOrganizationFilters: (state) => state.masters.organization.filters,
  selectOrganizationPagination: (state) => state.masters.organization.pagination,
  
  // Combined selectors
  selectMastersState: (state) => state.masters,
  selectAllMastersLoading: (state) => 
    state.masters.tenant.loading || 
    state.masters.user.loading || 
    state.masters.organization.loading,
  selectAnyMastersError: (state) => 
    state.masters.tenant.error || 
    state.masters.user.error || 
    state.masters.organization.error,
  
  // Helper selectors
  selectTenantById: (state, tenantId) => 
    state.masters.tenant.items.find(tenant => tenant.id === tenantId),
  selectUserById: (state, userId) => 
    state.masters.user.items.find(user => user.id === userId),
  selectOrganizationById: (state, orgId) => 
    state.masters.organization.items.find(org => org.id === orgId),
  selectUserPermissionsById: (state, userId) => 
    state.masters.user.userPermissions[userId] || [],
  selectUsersForRole: (state, roleId) => 
    state.masters.user.usersByRole[roleId] || [],
  selectUsersForOrganization: (state, orgId) => 
    state.masters.user.usersByOrganization[orgId] || [],
};

export default mastersReducerWithThunks;
