import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Loading states
  loading: {
    global: false,
    auth: false,
    organizations: false,
    users: false,
    quickbooks: false,
    reports: false,
  },
  
  // Modal states
  modals: {
    confirmDialog: {
      isOpen: false,
      title: '',
      message: '',
      onConfirm: null,
      onCancel: null,
      confirmText: 'Confirm',
      cancelText: 'Cancel',
      type: 'info', // 'info', 'warning', 'error', 'success'
    },
    organizationModal: {
      isOpen: false,
      mode: 'create', // 'create', 'edit', 'view'
      data: null,
    },
    userModal: {
      isOpen: false,
      mode: 'create',
      data: null,
    },
    quickbooksModal: {
      isOpen: false,
      mode: 'create',
      data: null,
    },
  },
  
  // Toast notifications
  toasts: [],
  
  // Sidebar and navigation
  sidebar: {
    isOpen: true,
    isMobile: false,
    activeMenu: null,
  },
  
  // Theme and preferences
  theme: {
    mode: 'light', // 'light', 'dark'
    primaryColor: '#1976d2',
    sidebarCollapsed: false,
  },
  
  // Page states
  page: {
    title: '',
    breadcrumbs: [],
    actions: [],
  },
  
  // Table states
  tables: {
    organizations: {
      selectedRows: [],
      sortBy: 'name',
      sortOrder: 'asc',
      filters: {},
    },
    users: {
      selectedRows: [],
      sortBy: 'name',
      sortOrder: 'asc',
      filters: {},
    },
    quickbooks: {
      selectedRows: [],
      sortBy: 'company_name',
      sortOrder: 'asc',
      filters: {},
    },
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Loading actions
    setGlobalLoading: (state, action) => {
      state.loading.global = action.payload;
    },
    setLoading: (state, action) => {
      const { key, value } = action.payload;
      if (state.loading.hasOwnProperty(key)) {
        state.loading[key] = value;
      }
    },
    clearAllLoading: (state) => {
      Object.keys(state.loading).forEach(key => {
        state.loading[key] = false;
      });
    },
    
    // Modal actions
    openModal: (state, action) => {
      const { modalName, mode = 'create', data = null } = action.payload;
      if (state.modals[modalName]) {
        state.modals[modalName].isOpen = true;
        state.modals[modalName].mode = mode;
        state.modals[modalName].data = data;
      }
    },
    closeModal: (state, action) => {
      const modalName = action.payload;
      if (state.modals[modalName]) {
        state.modals[modalName].isOpen = false;
        state.modals[modalName].data = null;
      }
    },
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(modalName => {
        state.modals[modalName].isOpen = false;
        state.modals[modalName].data = null;
      });
    },
    
    // Confirm dialog actions
    openConfirmDialog: (state, action) => {
      const { title, message, onConfirm, onCancel, confirmText, cancelText, type } = action.payload;
      state.modals.confirmDialog = {
        isOpen: true,
        title: title || 'Confirm Action',
        message: message || 'Are you sure?',
        onConfirm,
        onCancel,
        confirmText: confirmText || 'Confirm',
        cancelText: cancelText || 'Cancel',
        type: type || 'info',
      };
    },
    closeConfirmDialog: (state) => {
      state.modals.confirmDialog.isOpen = false;
    },
    
    // Toast actions
    addToast: (state, action) => {
      const { id, type, title, message, duration = 5000 } = action.payload;
      const toast = {
        id: id || Date.now().toString(),
        type: type || 'info', // 'success', 'error', 'warning', 'info'
        title: title || '',
        message: message || '',
        duration,
        timestamp: Date.now(),
      };
      state.toasts.push(toast);
    },
    removeToast: (state, action) => {
      const toastId = action.payload;
      state.toasts = state.toasts.filter(toast => toast.id !== toastId);
    },
    clearAllToasts: (state) => {
      state.toasts = [];
    },
    
    // Sidebar actions
    toggleSidebar: (state) => {
      state.sidebar.isOpen = !state.sidebar.isOpen;
    },
    setSidebarOpen: (state, action) => {
      state.sidebar.isOpen = action.payload;
    },
    setMobileMode: (state, action) => {
      state.sidebar.isMobile = action.payload;
    },
    setActiveMenu: (state, action) => {
      state.sidebar.activeMenu = action.payload;
    },
    
    // Theme actions
    setThemeMode: (state, action) => {
      state.theme.mode = action.payload;
      localStorage.setItem('themeMode', action.payload);
    },
    setPrimaryColor: (state, action) => {
      state.theme.primaryColor = action.payload;
      localStorage.setItem('primaryColor', action.payload);
    },
    toggleSidebarCollapsed: (state) => {
      state.theme.sidebarCollapsed = !state.theme.sidebarCollapsed;
      localStorage.setItem('sidebarCollapsed', state.theme.sidebarCollapsed.toString());
    },
    
    // Page actions
    setPageTitle: (state, action) => {
      state.page.title = action.payload;
    },
    setBreadcrumbs: (state, action) => {
      state.page.breadcrumbs = action.payload;
    },
    setPageActions: (state, action) => {
      state.page.actions = action.payload;
    },
    
    // Table actions
    setTableSelection: (state, action) => {
      const { tableName, selectedRows } = action.payload;
      if (state.tables[tableName]) {
        state.tables[tableName].selectedRows = selectedRows;
      }
    },
    setTableSort: (state, action) => {
      const { tableName, sortBy, sortOrder } = action.payload;
      if (state.tables[tableName]) {
        state.tables[tableName].sortBy = sortBy;
        state.tables[tableName].sortOrder = sortOrder;
      }
    },
    setTableFilters: (state, action) => {
      const { tableName, filters } = action.payload;
      if (state.tables[tableName]) {
        state.tables[tableName].filters = { ...state.tables[tableName].filters, ...filters };
      }
    },
    clearTableFilters: (state, action) => {
      const tableName = action.payload;
      if (state.tables[tableName]) {
        state.tables[tableName].filters = {};
      }
    },
    
    // Initialize UI from localStorage
    initializeUI: (state) => {
      const themeMode = localStorage.getItem('themeMode');
      const primaryColor = localStorage.getItem('primaryColor');
      const sidebarCollapsed = localStorage.getItem('sidebarCollapsed');
      
      if (themeMode) {
        state.theme.mode = themeMode;
      }
      if (primaryColor) {
        state.theme.primaryColor = primaryColor;
      }
      if (sidebarCollapsed) {
        state.theme.sidebarCollapsed = sidebarCollapsed === 'true';
      }
    },
  },
});

export const {
  // Loading actions
  setGlobalLoading,
  setLoading,
  clearAllLoading,
  
  // Modal actions
  openModal,
  closeModal,
  closeAllModals,
  openConfirmDialog,
  closeConfirmDialog,
  
  // Toast actions
  addToast,
  removeToast,
  clearAllToasts,
  
  // Sidebar actions
  toggleSidebar,
  setSidebarOpen,
  setMobileMode,
  setActiveMenu,
  
  // Theme actions
  setThemeMode,
  setPrimaryColor,
  toggleSidebarCollapsed,
  
  // Page actions
  setPageTitle,
  setBreadcrumbs,
  setPageActions,
  
  // Table actions
  setTableSelection,
  setTableSort,
  setTableFilters,
  clearTableFilters,
  
  // Initialize
  initializeUI,
} = uiSlice.actions;

export default uiSlice.reducer;
