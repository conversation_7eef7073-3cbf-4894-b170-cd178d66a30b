{"[markdown]": {"editor.unicodeHighlight.ambiguousCharacters": false, "editor.unicodeHighlight.invisibleCharacters": false, "diffEditor.ignoreTrimWhitespace": false, "editor.wordWrap": "on", "cSpell.fixSpellingWithRenameProvider": true, "cSpell.advanced.feature.useReferenceProviderWithRename": true, "cSpell.advanced.feature.useReferenceProviderRemove": "/^#+\\s/", "editor.codeActionsOnSave": {"source.organizeImports": "never"}}}