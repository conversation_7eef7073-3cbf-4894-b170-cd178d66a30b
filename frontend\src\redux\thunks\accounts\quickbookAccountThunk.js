import { createAsyncThunk } from '@reduxjs/toolkit';
import { handleAsyncError } from '../../../utils/methods/redux';
import api from '../../../lib/axios';

// Bulk sync QuickBooks accounts thunk
export const bulkSyncQuickbookAccounts = createAsyncThunk(
  'quickbookAccount/bulkSync',
  async ({ accountIds, organization_id, syncOptions = {} }, { rejectWithValue }) => {
    try {
      const response = await api.post('/quickbooks/accounts/bulk-sync', {
        accountIds,
        organization_id,
        options: syncOptions,
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Generate comprehensive report thunk
export const generateComprehensiveReport = createAsyncThunk(
  'quickbookAccount/generateReport',
  async ({ accountId, reportConfig, dateRange }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/quickbooks/accounts/${accountId}/comprehensive-report`, {
        config: reportConfig,
        startDate: dateRange.start,
        endDate: dateRange.end,
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Reconcile QuickBooks data thunk
export const reconcileQuickbookData = createAsyncThunk(
  'quickbookAccount/reconcile',
  async ({ accountId, reconciliationData }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/quickbooks/accounts/${accountId}/reconcile`, {
        data: reconciliationData,
        timestamp: new Date().toISOString(),
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Backup QuickBooks data thunk
export const backupQuickbookData = createAsyncThunk(
  'quickbookAccount/backup',
  async ({ accountId, backupOptions = {} }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/quickbooks/accounts/${accountId}/backup`, {
        options: backupOptions,
        timestamp: new Date().toISOString(),
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Restore QuickBooks data thunk
export const restoreQuickbookData = createAsyncThunk(
  'quickbookAccount/restore',
  async ({ accountId, backupId, restoreOptions = {} }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/quickbooks/accounts/${accountId}/restore`, {
        backupId,
        options: restoreOptions,
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Validate QuickBooks connection thunk
export const validateQuickbookConnection = createAsyncThunk(
  'quickbookAccount/validateConnection',
  async (accountId, { rejectWithValue }) => {
    try {
      const response = await api.post(`/quickbooks/accounts/${accountId}/validate-connection`);
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Refresh QuickBooks token thunk
export const refreshQuickbookToken = createAsyncThunk(
  'quickbookAccount/refreshToken',
  async (accountId, { rejectWithValue }) => {
    try {
      const response = await api.post(`/quickbooks/accounts/${accountId}/refresh-token`);
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Export QuickBooks data thunk
export const exportQuickbookData = createAsyncThunk(
  'quickbookAccount/export',
  async ({ accountId, exportConfig, format = 'csv' }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/quickbooks/accounts/${accountId}/export`, {
        config: exportConfig,
        format,
      }, {
        responseType: 'blob',
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `quickbooks-data.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return { success: true, message: 'Export completed successfully' };
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Schedule sync thunk
export const scheduleQuickbookSync = createAsyncThunk(
  'quickbookAccount/scheduleSync',
  async ({ accountId, schedule, syncOptions = {} }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/quickbooks/accounts/${accountId}/schedule-sync`, {
        schedule,
        options: syncOptions,
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Get sync history thunk
export const getQuickbookSyncHistory = createAsyncThunk(
  'quickbookAccount/syncHistory',
  async ({ accountId, limit = 50, offset = 0 }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/quickbooks/accounts/${accountId}/sync-history`, {
        params: { limit, offset },
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

export default {
  bulkSyncQuickbookAccounts,
  generateComprehensiveReport,
  reconcileQuickbookData,
  backupQuickbookData,
  restoreQuickbookData,
  validateQuickbookConnection,
  refreshQuickbookToken,
  exportQuickbookData,
  scheduleQuickbookSync,
  getQuickbookSyncHistory,
};
