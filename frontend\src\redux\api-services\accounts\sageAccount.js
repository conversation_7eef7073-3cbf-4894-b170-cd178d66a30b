import { api } from "../api";

// Sage Account API endpoints using RTK Query
export const sageAccountApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all Sage accounts
    getSageAccounts: builder.query({
      query: (params = {}) => ({
        url: "/sage/accounts",
        params: {
          organization_id: params.organization_id,
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          ...params,
        },
      }),
      providesTags: ["Sage"],
      transformResponse: (response) => response.data,
    }),

    // Get Sage account by ID
    getSageAccountById: builder.query({
      query: (id) => `/sage/accounts/${id}`,
      providesTags: (result, error, id) => [{ type: "Sage", id }],
      transformResponse: (response) => response.data,
    }),

    // Create Sage account
    createSageAccount: builder.mutation({
      query: (accountData) => ({
        url: "/sage/accounts",
        method: "POST",
        body: accountData,
      }),
      invalidatesTags: ["Sage"],
      transformResponse: (response) => response.data,
    }),

    // Update Sage account
    updateSageAccount: builder.mutation({
      query: ({ id, ...accountData }) => ({
        url: `/sage/accounts/${id}`,
        method: "PUT",
        body: accountData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Sage", id },
        "Sage",
      ],
      transformResponse: (response) => response.data,
    }),

    // Update Sage account status
    updateSageAccountStatus: builder.mutation({
      query: ({ id, status }) => ({
        url: `/sage/accounts/${id}/status`,
        method: "PUT",
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Sage", id },
        "Sage",
      ],
      transformResponse: (response) => response.data,
    }),

    // Sync Sage account
    syncSageAccount: builder.mutation({
      query: ({ id, organization_id }) => ({
        url: `/sage/accounts/${id}/sync`,
        method: "POST",
        body: { organization_id },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Sage", id },
        "Sage",
      ],
      transformResponse: (response) => response.data,
    }),

    // Delete Sage account
    deleteSageAccount: builder.mutation({
      query: (id) => ({
        url: `/sage/accounts/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Sage"],
      transformResponse: (response) => response.data,
    }),

    // Get Sage connection status
    getSageConnectionStatus: builder.query({
      query: (accountId) => `/sage/accounts/${accountId}/status`,
      providesTags: (result, error, accountId) => [
        { type: "Sage", id: `${accountId}-status` },
      ],
      transformResponse: (response) => response.data,
    }),

    // Test Sage connection
    testSageConnection: builder.mutation({
      query: (accountId) => ({
        url: `/sage/accounts/${accountId}/test-connection`,
        method: "POST",
      }),
      transformResponse: (response) => response.data,
    }),

    // Get Sage reports
    getSageReports: builder.query({
      query: ({ accountId, reportType, ...params }) => ({
        url: `/sage/accounts/${accountId}/reports/${reportType}`,
        params,
      }),
      providesTags: ["Reports"],
      transformResponse: (response) => response.data,
    }),

    // Fetch Sage data
    fetchSageData: builder.mutation({
      query: ({ accountId, dataType, ...params }) => ({
        url: `/sage/accounts/${accountId}/fetch/${dataType}`,
        method: "POST",
        body: params,
      }),
      invalidatesTags: ["Sage", "Reports"],
      transformResponse: (response) => response.data,
    }),

    // Import Sage data
    importSageData: builder.mutation({
      query: ({ accountId, fileData }) => ({
        url: `/sage/accounts/${accountId}/import`,
        method: "POST",
        body: fileData,
      }),
      invalidatesTags: ["Sage", "Reports"],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Account management hooks
  useGetSageAccountsQuery,
  useGetSageAccountByIdQuery,
  useCreateSageAccountMutation,
  useUpdateSageAccountMutation,
  useUpdateSageAccountStatusMutation,
  useSyncSageAccountMutation,
  useDeleteSageAccountMutation,

  // Connection hooks
  useGetSageConnectionStatusQuery,
  useTestSageConnectionMutation,

  // Reports and data hooks
  useGetSageReportsQuery,
  useFetchSageDataMutation,
  useImportSageDataMutation,
} = sageAccountApi;
