import { api } from './api';

// QuickBooks API endpoints using RTK Query
export const quickbooksApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all QuickBooks accounts
    getQuickbooksAccounts: builder.query({
      query: (params = {}) => ({
        url: '/quickbooks/accounts',
        params: {
          organization_id: params.organization_id,
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          ...params,
        },
      }),
      providesTags: ['QuickBooks'],
      transformResponse: (response) => response.data,
    }),

    // Get QuickBooks account by ID
    getQuickbooksAccountById: builder.query({
      query: (id) => `/quickbooks/accounts/${id}`,
      providesTags: (result, error, id) => [{ type: 'QuickBooks', id }],
      transformResponse: (response) => response.data,
    }),

    // Create/Add QuickBooks account
    addQuickbooksAccount: builder.mutation({
      query: (accountData) => ({
        url: '/quickbooks/add',
        method: 'GET',
        params: accountData, // Using GET with query params as per existing implementation
      }),
      invalidatesTags: ['QuickBooks'],
      transformResponse: (response) => response.data,
    }),

    // Update QuickBooks account status
    updateQuickbooksAccountStatus: builder.mutation({
      query: ({ id, status }) => ({
        url: `/quickbooks/status/${id}`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'QuickBooks', id },
        'QuickBooks',
      ],
      transformResponse: (response) => response.data,
    }),

    // Sync QuickBooks account
    syncQuickbooksAccount: builder.mutation({
      query: ({ id, organization_id }) => ({
        url: `/quickbooks/accounts/${id}/sync`,
        method: 'POST',
        body: { organization_id },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'QuickBooks', id },
        'QuickBooks',
      ],
      transformResponse: (response) => response.data,
    }),

    // Delete QuickBooks account
    deleteQuickbooksAccount: builder.mutation({
      query: (id) => ({
        url: `/quickbooks/accounts/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['QuickBooks'],
      transformResponse: (response) => response.data,
    }),

    // Get QuickBooks OAuth URL
    getQuickbooksOAuthUrl: builder.query({
      query: () => '/quickbooks/oauth-url',
      transformResponse: (response) => response.data,
    }),

    // Get QuickBooks tokens
    getQuickbooksTokens: builder.mutation({
      query: (tokenData) => ({
        url: '/quickbooks/token',
        method: 'POST',
        body: tokenData,
      }),
      transformResponse: (response) => response.data,
    }),

    // Add QuickBooks tokens
    addQuickbooksTokens: builder.mutation({
      query: (tokenData) => ({
        url: '/quickbooks/add-token',
        method: 'POST',
        body: tokenData,
      }),
      invalidatesTags: ['QuickBooks'],
      transformResponse: (response) => response.data,
    }),

    // Save QuickBooks file
    saveQuickbooksFile: builder.mutation({
      query: (fileData) => ({
        url: '/quickbooks',
        method: 'POST',
        body: fileData,
      }),
      transformResponse: (response) => response.data,
    }),

    // Get QuickBooks reports
    getQuickbooksReports: builder.query({
      query: ({ accountId, reportType, ...params }) => ({
        url: `/quickbooks/accounts/${accountId}/reports/${reportType}`,
        params,
      }),
      providesTags: ['Reports'],
      transformResponse: (response) => response.data,
    }),

    // Fetch Trial Balance
    fetchTrialBalance: builder.mutation({
      query: (reportData) => ({
        url: '/reports/trial-balance',
        method: 'POST',
        body: reportData,
      }),
      invalidatesTags: ['Reports'],
      transformResponse: (response) => response.data,
    }),

    // Fetch Profit & Loss
    fetchProfitLoss: builder.mutation({
      query: (reportData) => ({
        url: '/reports/profit-loss',
        method: 'POST',
        body: reportData,
      }),
      invalidatesTags: ['Reports'],
      transformResponse: (response) => response.data,
    }),

    // Fetch Balance Sheet
    fetchBalanceSheet: builder.mutation({
      query: (reportData) => ({
        url: '/reports/balance-sheet',
        method: 'POST',
        body: reportData,
      }),
      invalidatesTags: ['Reports'],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  // Account management hooks
  useGetQuickbooksAccountsQuery,
  useGetQuickbooksAccountByIdQuery,
  useAddQuickbooksAccountMutation,
  useUpdateQuickbooksAccountStatusMutation,
  useSyncQuickbooksAccountMutation,
  useDeleteQuickbooksAccountMutation,
  
  // OAuth and token hooks
  useGetQuickbooksOAuthUrlQuery,
  useGetQuickbooksTokensMutation,
  useAddQuickbooksTokensMutation,
  
  // File operations
  useSaveQuickbooksFileMutation,
  
  // Reports hooks
  useGetQuickbooksReportsQuery,
  useFetchTrialBalanceMutation,
  useFetchProfitLossMutation,
  useFetchBalanceSheetMutation,
} = quickbooksApi;
