import { combineReducers } from '@reduxjs/toolkit';

// Import auth sub-slices
import loginReducer from '../slices/auth/loginSlice';
import profileReducer from '../slices/auth/profileSlice';
import passwordReducer from '../slices/auth/passwordSlice';

// Import auth thunks for extraReducers
import loginThunks from '../thunks/auth/loginThunk';
import { createAsyncExtraReducers } from '../../utils/methods/redux';

// Combine all auth-related reducers
const authReducer = combineReducers({
  login: loginReducer,
  profile: profileReducer,
  password: passwordReducer,
});

// Create a wrapper to add extra reducers for thunks
const authReducerWithThunks = (state, action) => {
  // First, let the combined reducer handle the action
  const newState = authReducer(state, action);
  
  // Then handle thunk actions that affect multiple sub-slices
  switch (action.type) {
    // <PERSON>le auto login success - affects both login and profile
    case loginThunks.autoLogin.fulfilled.type:
      return {
        ...newState,
        login: {
          ...newState.login,
          token: action.payload.token,
          isAuthenticated: true,
          rememberMe: action.payload.rememberMe,
          lastLoginTime: new Date().toISOString(),
          error: null,
        },
        profile: {
          ...newState.profile,
          user: action.payload.user,
          lastUpdated: new Date().toISOString(),
          error: null,
        },
      };
    
    // Handle perform login success - affects both login and profile
    case loginThunks.performLogin.fulfilled.type:
      return {
        ...newState,
        login: {
          ...newState.login,
          token: action.payload.data?.token,
          isAuthenticated: true,
          rememberMe: action.payload.rememberMe,
          lastLoginTime: new Date().toISOString(),
          loginAttempts: 0,
          error: null,
        },
        profile: {
          ...newState.profile,
          user: action.payload.data?.user,
          lastUpdated: new Date().toISOString(),
          error: null,
        },
      };
    
    // Handle perform logout success - clears all auth state
    case loginThunks.performLogout.fulfilled.type:
      return {
        login: {
          ...newState.login,
          token: null,
          isAuthenticated: false,
          loginAttempts: 0,
          lastLoginTime: null,
          rememberMe: false,
          error: null,
        },
        profile: {
          ...newState.profile,
          user: null,
          preferences: {},
          avatar: null,
          lastUpdated: null,
          error: null,
        },
        password: {
          ...newState.password,
          resetToken: null,
          resetEmail: null,
          forgotPasswordSent: false,
          error: null,
        },
      };
    
    // Handle token refresh success
    case loginThunks.refreshAuthToken.fulfilled.type:
      return {
        ...newState,
        login: {
          ...newState.login,
          token: action.payload.token,
          isAuthenticated: true,
          error: null,
        },
        profile: {
          ...newState.profile,
          user: action.payload.user || newState.profile.user,
          lastUpdated: new Date().toISOString(),
          error: null,
        },
      };
    
    // Handle social login success
    case loginThunks.socialLogin.fulfilled.type:
      return {
        ...newState,
        login: {
          ...newState.login,
          token: action.payload.data?.token,
          isAuthenticated: true,
          lastLoginTime: new Date().toISOString(),
          loginAttempts: 0,
          error: null,
        },
        profile: {
          ...newState.profile,
          user: action.payload.data?.user,
          lastUpdated: new Date().toISOString(),
          error: null,
        },
      };
    
    // Handle two-factor verification success
    case loginThunks.verifyTwoFactor.fulfilled.type:
      return {
        ...newState,
        login: {
          ...newState.login,
          token: action.payload.data?.token,
          isAuthenticated: true,
          lastLoginTime: new Date().toISOString(),
          error: null,
        },
        profile: {
          ...newState.profile,
          user: action.payload.data?.user,
          lastUpdated: new Date().toISOString(),
          error: null,
        },
      };
    
    // Handle auth errors that affect multiple slices
    case loginThunks.autoLogin.rejected.type:
    case loginThunks.performLogin.rejected.type:
    case loginThunks.refreshAuthToken.rejected.type:
      return {
        ...newState,
        login: {
          ...newState.login,
          isAuthenticated: false,
          token: null,
          error: action.payload,
        },
      };
    
    default:
      return newState;
  }
};

// Export selectors for the auth module
export const authSelectors = {
  // Login selectors
  selectIsAuthenticated: (state) => state.auth.login.isAuthenticated,
  selectToken: (state) => state.auth.login.token,
  selectLoginAttempts: (state) => state.auth.login.loginAttempts,
  selectLastLoginTime: (state) => state.auth.login.lastLoginTime,
  selectRememberMe: (state) => state.auth.login.rememberMe,
  selectLoginLoading: (state) => state.auth.login.loading,
  selectLoginError: (state) => state.auth.login.error,
  
  // Profile selectors
  selectUser: (state) => state.auth.profile.user,
  selectUserPreferences: (state) => state.auth.profile.preferences,
  selectUserAvatar: (state) => state.auth.profile.avatar,
  selectProfileLastUpdated: (state) => state.auth.profile.lastUpdated,
  selectProfileLoading: (state) => state.auth.profile.loading,
  selectProfileError: (state) => state.auth.profile.error,
  
  // Password selectors
  selectResetToken: (state) => state.auth.password.resetToken,
  selectResetEmail: (state) => state.auth.password.resetEmail,
  selectPasswordStrength: (state) => state.auth.password.passwordStrength,
  selectLastPasswordChange: (state) => state.auth.password.lastPasswordChange,
  selectForgotPasswordSent: (state) => state.auth.password.forgotPasswordSent,
  selectPasswordLoading: (state) => state.auth.password.loading,
  selectPasswordError: (state) => state.auth.password.error,
  
  // Combined selectors
  selectAuthState: (state) => state.auth,
  selectIsLoggedIn: (state) => state.auth.login.isAuthenticated && state.auth.login.token,
  selectUserWithAuth: (state) => ({
    user: state.auth.profile.user,
    isAuthenticated: state.auth.login.isAuthenticated,
    token: state.auth.login.token,
  }),
};

export default authReducerWithThunks;
