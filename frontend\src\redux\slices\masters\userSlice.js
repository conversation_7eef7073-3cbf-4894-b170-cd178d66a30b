import { createStandardSlice } from '../../../utils/methods/redux';
import { userApi } from '../../api-services/masters/user';

const initialState = {
  users: [],
  userPermissions: {},
  usersByRole: {},
  usersByOrganization: {},
};

const userSlice = createStandardSlice('user', initialState, {
  // Custom reducers specific to user management
  setUserPermissions: (state, action) => {
    const { userId, permissions } = action.payload;
    state.userPermissions[userId] = permissions;
  },
  
  setUsersByRole: (state, action) => {
    const { roleId, users } = action.payload;
    state.usersByRole[roleId] = users;
  },
  
  setUsersByOrganization: (state, action) => {
    const { organizationId, users } = action.payload;
    state.usersByOrganization[organizationId] = users;
  },
  
  updateUserInList: (state, action) => {
    const { id, data } = action.payload;
    const index = state.users.findIndex(user => user.id === id);
    if (index !== -1) {
      state.users[index] = { ...state.users[index], ...data };
    }
  },
  
  addUserToList: (state, action) => {
    state.users.push(action.payload);
  },
  
  removeUserFromList: (state, action) => {
    const id = action.payload;
    state.users = state.users.filter(user => user.id !== id);
    // Clean up related data
    delete state.userPermissions[id];
  },
  
  clearUserData: (state) => {
    state.users = [];
    state.userPermissions = {};
    state.usersByRole = {};
    state.usersByOrganization = {};
    state.selectedItem = null;
    state.error = null;
  },
});

// Add extra reducers for RTK Query
userSlice.caseReducers = {
  ...userSlice.caseReducers,
  extraReducers: (builder) => {
    // Handle users fetch success
    builder.addMatcher(
      userApi.endpoints.getUsers.matchFulfilled,
      (state, action) => {
        state.users = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle user fetch by ID success
    builder.addMatcher(
      userApi.endpoints.getUserById.matchFulfilled,
      (state, action) => {
        state.selectedItem = action.payload;
        state.error = null;
      }
    );

    // Handle user create success
    builder.addMatcher(
      userApi.endpoints.createUser.matchFulfilled,
      (state, action) => {
        state.users.push(action.payload);
        state.error = null;
      }
    );

    // Handle user update success
    builder.addMatcher(
      userApi.endpoints.updateUser.matchFulfilled,
      (state, action) => {
        const updatedUser = action.payload;
        const index = state.users.findIndex(user => user.id === updatedUser.id);
        if (index !== -1) {
          state.users[index] = updatedUser;
        }
        state.error = null;
      }
    );

    // Handle user delete success
    builder.addMatcher(
      userApi.endpoints.deleteUser.matchFulfilled,
      (state, action) => {
        const deletedId = action.meta.arg.originalArgs || action.meta.arg;
        state.users = state.users.filter(user => user.id !== deletedId);
        delete state.userPermissions[deletedId];
        state.error = null;
      }
    );

    // Handle user permissions fetch success
    builder.addMatcher(
      userApi.endpoints.getUserPermissions.matchFulfilled,
      (state, action) => {
        const userId = action.meta.arg.originalArgs || action.meta.arg;
        state.userPermissions[userId] = action.payload;
        state.error = null;
      }
    );

    // Handle user permissions update success
    builder.addMatcher(
      userApi.endpoints.updateUserPermissions.matchFulfilled,
      (state, action) => {
        const { userId } = action.meta.arg.originalArgs || action.meta.arg;
        state.userPermissions[userId] = action.payload;
        state.error = null;
      }
    );

    // Handle users by role fetch success
    builder.addMatcher(
      userApi.endpoints.getUsersByRole.matchFulfilled,
      (state, action) => {
        const roleId = action.meta.arg.originalArgs || action.meta.arg;
        state.usersByRole[roleId] = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle users by organization fetch success
    builder.addMatcher(
      userApi.endpoints.getUsersByOrganization.matchFulfilled,
      (state, action) => {
        const organizationId = action.meta.arg.originalArgs || action.meta.arg;
        state.usersByOrganization[organizationId] = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle any user API errors
    builder.addMatcher(
      (action) => action.type.endsWith('/rejected') && action.type.includes('user'),
      (state, action) => {
        state.error = action.payload?.message || 'User operation failed';
      }
    );
  },
};

export const {
  setUserPermissions,
  setUsersByRole,
  setUsersByOrganization,
  updateUserInList,
  addUserToList,
  removeUserFromList,
  clearUserData,
  setLoading,
  setError,
  clearError,
  setItems,
  setSelectedItem,
  clearSelectedItem,
  setFilters,
  resetFilters,
  setPagination,
} = userSlice.actions;

export default userSlice.reducer;
