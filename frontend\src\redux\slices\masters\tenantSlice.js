import { createStandardSlice } from '../../../utils/methods/redux';
import { tenantApi } from '../../api-services/masters/tenant';

const initialState = {
  tenants: [],
  currentTenant: null,
  tenantSettings: {},
};

const tenantSlice = createStandardSlice('tenant', initialState, {
  // Custom reducers specific to tenant management
  setCurrentTenant: (state, action) => {
    state.currentTenant = action.payload;
  },
  
  clearCurrentTenant: (state) => {
    state.currentTenant = null;
  },
  
  setTenantSettings: (state, action) => {
    state.tenantSettings = { ...state.tenantSettings, ...action.payload };
  },
  
  updateTenantInList: (state, action) => {
    const { id, data } = action.payload;
    const index = state.tenants.findIndex(tenant => tenant.id === id);
    if (index !== -1) {
      state.tenants[index] = { ...state.tenants[index], ...data };
    }
  },
  
  addTenantToList: (state, action) => {
    state.tenants.push(action.payload);
  },
  
  removeTenantFromList: (state, action) => {
    const id = action.payload;
    state.tenants = state.tenants.filter(tenant => tenant.id !== id);
  },
});

// Add extra reducers for RTK Query
tenantSlice.caseReducers = {
  ...tenantSlice.caseReducers,
  extraReducers: (builder) => {
    // Handle tenants fetch success
    builder.addMatcher(
      tenantApi.endpoints.getTenants.matchFulfilled,
      (state, action) => {
        state.tenants = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle tenant fetch by ID success
    builder.addMatcher(
      tenantApi.endpoints.getTenantById.matchFulfilled,
      (state, action) => {
        state.selectedItem = action.payload;
        state.error = null;
      }
    );

    // Handle tenant create success
    builder.addMatcher(
      tenantApi.endpoints.createTenant.matchFulfilled,
      (state, action) => {
        state.tenants.push(action.payload);
        state.error = null;
      }
    );

    // Handle tenant update success
    builder.addMatcher(
      tenantApi.endpoints.updateTenant.matchFulfilled,
      (state, action) => {
        const updatedTenant = action.payload;
        const index = state.tenants.findIndex(tenant => tenant.id === updatedTenant.id);
        if (index !== -1) {
          state.tenants[index] = updatedTenant;
        }
        if (state.currentTenant?.id === updatedTenant.id) {
          state.currentTenant = updatedTenant;
        }
        state.error = null;
      }
    );

    // Handle tenant delete success
    builder.addMatcher(
      tenantApi.endpoints.deleteTenant.matchFulfilled,
      (state, action) => {
        const deletedId = action.meta.arg.originalArgs || action.meta.arg;
        state.tenants = state.tenants.filter(tenant => tenant.id !== deletedId);
        if (state.currentTenant?.id === deletedId) {
          state.currentTenant = null;
        }
        state.error = null;
      }
    );

    // Handle tenant settings fetch success
    builder.addMatcher(
      tenantApi.endpoints.getTenantSettings.matchFulfilled,
      (state, action) => {
        state.tenantSettings = action.payload;
        state.error = null;
      }
    );

    // Handle tenant settings update success
    builder.addMatcher(
      tenantApi.endpoints.updateTenantSettings.matchFulfilled,
      (state, action) => {
        state.tenantSettings = { ...state.tenantSettings, ...action.payload };
        state.error = null;
      }
    );

    // Handle any tenant API errors
    builder.addMatcher(
      (action) => action.type.endsWith('/rejected') && action.type.includes('tenant'),
      (state, action) => {
        state.error = action.payload?.message || 'Tenant operation failed';
      }
    );
  },
};

export const {
  setCurrentTenant,
  clearCurrentTenant,
  setTenantSettings,
  updateTenantInList,
  addTenantToList,
  removeTenantFromList,
  setLoading,
  setError,
  clearError,
  setItems,
  setSelectedItem,
  clearSelectedItem,
  setFilters,
  resetFilters,
  setPagination,
} = tenantSlice.actions;

export default tenantSlice.reducer;
