import { api } from '../api';

// User API endpoints using RTK Query
export const userApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all users
    getUsers: builder.query({
      query: (params = {}) => ({
        url: '/users',
        params: {
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          ...params,
        },
      }),
      providesTags: ['User'],
      transformResponse: (response) => response.data,
    }),

    // Get user by ID
    getUserById: builder.query({
      query: (id) => `/users/${id}`,
      providesTags: (result, error, id) => [{ type: 'User', id }],
      transformResponse: (response) => response.data,
    }),

    // Create user
    createUser: builder.mutation({
      query: (userData) => ({
        url: '/users',
        method: 'POST',
        body: userData,
      }),
      invalidatesTags: ['User'],
      transformResponse: (response) => response.data,
    }),

    // Update user
    updateUser: builder.mutation({
      query: ({ id, ...userData }) => ({
        url: `/users/${id}`,
        method: 'PUT',
        body: userData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'User', id },
        'User',
      ],
      transformResponse: (response) => response.data,
    }),

    // Delete user (soft delete)
    deleteUser: builder.mutation({
      query: (id) => ({
        url: `/users/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['User'],
      transformResponse: (response) => response.data,
    }),

    // Get user permissions
    getUserPermissions: builder.query({
      query: (userId) => `/users/${userId}/permissions`,
      providesTags: (result, error, userId) => [{ type: 'User', id: `${userId}-permissions` }],
      transformResponse: (response) => response.data,
    }),

    // Update user permissions
    updateUserPermissions: builder.mutation({
      query: ({ userId, permissions }) => ({
        url: `/users/${userId}/permissions`,
        method: 'PUT',
        body: { permissions },
      }),
      invalidatesTags: (result, error, { userId }) => [
        { type: 'User', id: `${userId}-permissions` },
        { type: 'User', id: userId },
      ],
      transformResponse: (response) => response.data,
    }),

    // Get users by role
    getUsersByRole: builder.query({
      query: (roleId) => ({
        url: '/users',
        params: { roleId },
      }),
      providesTags: ['User'],
      transformResponse: (response) => response.data,
    }),

    // Get users by organization
    getUsersByOrganization: builder.query({
      query: (organizationId) => ({
        url: '/users',
        params: { organizationId },
      }),
      providesTags: ['User'],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useGetUsersQuery,
  useGetUserByIdQuery,
  useCreateUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useGetUserPermissionsQuery,
  useUpdateUserPermissionsMutation,
  useGetUsersByRoleQuery,
  useGetUsersByOrganizationQuery,
} = userApi;
