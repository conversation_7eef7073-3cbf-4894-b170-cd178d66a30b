import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

// Base query with authentication and error handling
const baseQuery = fetchBaseQuery({
  baseUrl: "/api",
  prepareHeaders: (headers, { getState }) => {
    // Get token from auth state
    const token = getState()?.auth?.token;

    // Set content type
    headers.set("Content-Type", "application/json");

    // Add authorization header if token exists
    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }

    return headers;
  },
});

// Base query with error handling and token refresh
const baseQueryWithReauth = async (args, api, extraOptions) => {
  let result = await baseQuery(args, api, extraOptions);

  // Handle 401 unauthorized errors
  if (result.error && result.error.status === 401) {
    // Try to refresh token
    const refreshResult = await baseQuery(
      {
        url: "/auth/refresh-token",
        method: "POST",
      },
      api,
      extraOptions
    );

    if (refreshResult.data) {
      // Store the new token
      api.dispatch(setToken(refreshResult.data.token));

      // Retry the original query
      result = await baseQuery(args, api, extraOptions);
    } else {
      // Refresh failed, logout user
      api.dispatch(clearAuth());
    }
  }

  return result;
};

// Create the base API slice
export const api = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    "Auth",
    "User",
    "Organization",
    "QuickBooks",
    "NetSuite",
    "Sage",
    "Masters",
    "Reports",
  ],
  endpoints: () => ({}), // Empty endpoints - will be extended by other API services
});

// Import auth actions for token management
import { setToken, clearAuth } from "../slices/authSlice";

export default api;
