import { api } from '../api';

// Profile API endpoints using RTK Query
export const profileApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get user profile
    getProfile: builder.query({
      query: () => '/auth/me',
      providesTags: ['Auth'],
      transformResponse: (response) => response.data,
    }),

    // Update user profile
    updateProfile: builder.mutation({
      query: ({ userId, userData }) => ({
        url: `/auth/profile/${userId}`,
        method: 'PUT',
        body: userData,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useGetProfileQuery,
  useUpdateProfileMutation,
} = profileApi;
