import { createAsyncThunk } from '@reduxjs/toolkit';
import { handleAsyncError } from '../../../utils/methods/redux';
import api from '../../../lib/axios';

// Bulk tenant operations thunk
export const bulkTenantOperations = createAsyncThunk(
  'tenant/bulkOperations',
  async ({ operation, tenantIds, data = {} }, { rejectWithValue }) => {
    try {
      const response = await api.post('/tenants/bulk', {
        operation,
        tenantIds,
        data,
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Import tenants from file thunk
export const importTenants = createAsyncThunk(
  'tenant/import',
  async ({ file, options = {} }, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('options', JSON.stringify(options));
      
      const response = await api.post('/tenants/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Export tenants thunk
export const exportTenants = createAsyncThunk(
  'tenant/export',
  async ({ format = 'csv', filters = {} }, { rejectWithValue }) => {
    try {
      const response = await api.post('/tenants/export', {
        format,
        filters,
      }, {
        responseType: 'blob',
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `tenants.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      return { success: true, message: 'Export completed successfully' };
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Validate tenant data thunk
export const validateTenantData = createAsyncThunk(
  'tenant/validate',
  async (tenantData, { rejectWithValue }) => {
    try {
      const response = await api.post('/tenants/validate', tenantData);
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Duplicate tenant thunk
export const duplicateTenant = createAsyncThunk(
  'tenant/duplicate',
  async ({ tenantId, newName, includeSettings = true }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/tenants/${tenantId}/duplicate`, {
        newName,
        includeSettings,
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Archive tenant thunk
export const archiveTenant = createAsyncThunk(
  'tenant/archive',
  async ({ tenantId, reason }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/tenants/${tenantId}/archive`, {
        reason,
        archivedAt: new Date().toISOString(),
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Restore tenant thunk
export const restoreTenant = createAsyncThunk(
  'tenant/restore',
  async (tenantId, { rejectWithValue }) => {
    try {
      const response = await api.post(`/tenants/${tenantId}/restore`);
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Sync tenant with external system thunk
export const syncTenantWithExternal = createAsyncThunk(
  'tenant/syncExternal',
  async ({ tenantId, externalSystem, syncOptions = {} }, { rejectWithValue }) => {
    try {
      const response = await api.post(`/tenants/${tenantId}/sync/${externalSystem}`, {
        options: syncOptions,
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

// Get tenant analytics thunk
export const getTenantAnalytics = createAsyncThunk(
  'tenant/analytics',
  async ({ tenantId, dateRange, metrics = [] }, { rejectWithValue }) => {
    try {
      const response = await api.get(`/tenants/${tenantId}/analytics`, {
        params: {
          startDate: dateRange.start,
          endDate: dateRange.end,
          metrics: metrics.join(','),
        },
      });
      
      return response.data;
    } catch (error) {
      const errorMessage = handleAsyncError(error);
      return rejectWithValue(errorMessage);
    }
  }
);

export default {
  bulkTenantOperations,
  importTenants,
  exportTenants,
  validateTenantData,
  duplicateTenant,
  archiveTenant,
  restoreTenant,
  syncTenantWithExternal,
  getTenantAnalytics,
};
