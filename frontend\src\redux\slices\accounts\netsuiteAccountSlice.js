import { createStandardSlice } from '../../../utils/methods/redux';
import { netsuiteAccountApi } from '../../api-services/accounts/netsuiteAccount';

const initialState = {
  accounts: [],
  connectionStatus: {},
  lastSynced: null,
  syncStatus: {},
  reports: {},
};

const netsuiteAccountSlice = createStandardSlice('netsuiteAccount', initialState, {
  // Custom reducers specific to NetSuite account management
  setConnectionStatus: (state, action) => {
    const { accountId, status } = action.payload;
    state.connectionStatus[accountId] = status;
  },
  
  setSyncStatus: (state, action) => {
    const { accountId, status } = action.payload;
    state.syncStatus[accountId] = status;
  },
  
  setLastSynced: (state, action) => {
    state.lastSynced = action.payload;
  },
  
  setReports: (state, action) => {
    const { accountId, reportType, data } = action.payload;
    if (!state.reports[accountId]) {
      state.reports[accountId] = {};
    }
    state.reports[accountId][reportType] = data;
  },
  
  updateAccountInList: (state, action) => {
    const { id, data } = action.payload;
    const index = state.accounts.findIndex(account => account.id === id);
    if (index !== -1) {
      state.accounts[index] = { ...state.accounts[index], ...data };
    }
  },
  
  addAccountToList: (state, action) => {
    state.accounts.push(action.payload);
  },
  
  removeAccountFromList: (state, action) => {
    const id = action.payload;
    state.accounts = state.accounts.filter(account => account.id !== id);
    delete state.connectionStatus[id];
    delete state.syncStatus[id];
    delete state.reports[id];
  },
  
  clearAccountData: (state) => {
    state.accounts = [];
    state.connectionStatus = {};
    state.lastSynced = null;
    state.syncStatus = {};
    state.reports = {};
    state.selectedItem = null;
    state.error = null;
  },
});

// Add extra reducers for RTK Query
netsuiteAccountSlice.caseReducers = {
  ...netsuiteAccountSlice.caseReducers,
  extraReducers: (builder) => {
    // Handle accounts fetch success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.getNetsuiteAccounts.matchFulfilled,
      (state, action) => {
        state.accounts = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle account fetch by ID success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.getNetsuiteAccountById.matchFulfilled,
      (state, action) => {
        state.selectedItem = action.payload;
        state.error = null;
      }
    );

    // Handle account create success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.createNetsuiteAccount.matchFulfilled,
      (state, action) => {
        state.accounts.push(action.payload);
        state.error = null;
      }
    );

    // Handle account update success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.updateNetsuiteAccount.matchFulfilled,
      (state, action) => {
        const updatedAccount = action.payload;
        const index = state.accounts.findIndex(account => account.id === updatedAccount.id);
        if (index !== -1) {
          state.accounts[index] = updatedAccount;
        }
        state.error = null;
      }
    );

    // Handle account status update success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.updateNetsuiteAccountStatus.matchFulfilled,
      (state, action) => {
        const updatedAccount = action.payload;
        const index = state.accounts.findIndex(account => account.id === updatedAccount.id);
        if (index !== -1) {
          state.accounts[index] = { ...state.accounts[index], ...updatedAccount };
        }
        state.error = null;
      }
    );

    // Handle account sync success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.syncNetsuiteAccount.matchFulfilled,
      (state, action) => {
        const syncedAccount = action.payload;
        const index = state.accounts.findIndex(account => account.id === syncedAccount.id);
        if (index !== -1) {
          state.accounts[index] = { ...state.accounts[index], ...syncedAccount };
        }
        state.lastSynced = new Date().toISOString();
        state.syncStatus[syncedAccount.id] = 'completed';
        state.error = null;
      }
    );

    // Handle account delete success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.deleteNetsuiteAccount.matchFulfilled,
      (state, action) => {
        const deletedId = action.meta.arg.originalArgs || action.meta.arg;
        state.accounts = state.accounts.filter(account => account.id !== deletedId);
        delete state.connectionStatus[deletedId];
        delete state.syncStatus[deletedId];
        delete state.reports[deletedId];
        state.error = null;
      }
    );

    // Handle connection status fetch success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.getNetsuiteConnectionStatus.matchFulfilled,
      (state, action) => {
        const accountId = action.meta.arg.originalArgs || action.meta.arg;
        state.connectionStatus[accountId] = action.payload;
        state.error = null;
      }
    );

    // Handle connection test success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.testNetsuiteConnection.matchFulfilled,
      (state, action) => {
        const accountId = action.meta.arg.originalArgs || action.meta.arg;
        state.connectionStatus[accountId] = action.payload;
        state.error = null;
      }
    );

    // Handle reports fetch success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.getNetsuiteReports.matchFulfilled,
      (state, action) => {
        const { accountId, reportType } = action.meta.arg.originalArgs || action.meta.arg;
        if (!state.reports[accountId]) {
          state.reports[accountId] = {};
        }
        state.reports[accountId][reportType] = action.payload;
        state.error = null;
      }
    );

    // Handle data fetch success
    builder.addMatcher(
      netsuiteAccountApi.endpoints.fetchNetsuiteData.matchFulfilled,
      (state, action) => {
        // Store fetched data
        state.data = action.payload;
        state.error = null;
      }
    );

    // Handle any NetSuite API errors
    builder.addMatcher(
      (action) => action.type.endsWith('/rejected') && action.type.includes('netsuite'),
      (state, action) => {
        state.error = action.payload?.message || 'NetSuite operation failed';
      }
    );
  },
};

export const {
  setConnectionStatus,
  setSyncStatus,
  setLastSynced,
  setReports,
  updateAccountInList,
  addAccountToList,
  removeAccountFromList,
  clearAccountData,
  setLoading,
  setError,
  clearError,
  setItems,
  setSelectedItem,
  clearSelectedItem,
  setFilters,
  resetFilters,
  setPagination,
} = netsuiteAccountSlice.actions;

export default netsuiteAccountSlice.reducer;
