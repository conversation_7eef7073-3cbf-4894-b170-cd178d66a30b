import { createStandardSlice } from '../../../utils/methods/redux';
import { sageAccountApi } from '../../api-services/accounts/sageAccount';

const initialState = {
  accounts: [],
  connectionStatus: {},
  lastSynced: null,
  syncStatus: {},
  reports: {},
  importHistory: [],
};

const sageAccountSlice = createStandardSlice('sageAccount', initialState, {
  // Custom reducers specific to Sage account management
  setConnectionStatus: (state, action) => {
    const { accountId, status } = action.payload;
    state.connectionStatus[accountId] = status;
  },
  
  setSyncStatus: (state, action) => {
    const { accountId, status } = action.payload;
    state.syncStatus[accountId] = status;
  },
  
  setLastSynced: (state, action) => {
    state.lastSynced = action.payload;
  },
  
  setReports: (state, action) => {
    const { accountId, reportType, data } = action.payload;
    if (!state.reports[accountId]) {
      state.reports[accountId] = {};
    }
    state.reports[accountId][reportType] = data;
  },
  
  addImportHistory: (state, action) => {
    state.importHistory.unshift(action.payload);
    // Keep only last 50 import records
    if (state.importHistory.length > 50) {
      state.importHistory = state.importHistory.slice(0, 50);
    }
  },
  
  updateAccountInList: (state, action) => {
    const { id, data } = action.payload;
    const index = state.accounts.findIndex(account => account.id === id);
    if (index !== -1) {
      state.accounts[index] = { ...state.accounts[index], ...data };
    }
  },
  
  addAccountToList: (state, action) => {
    state.accounts.push(action.payload);
  },
  
  removeAccountFromList: (state, action) => {
    const id = action.payload;
    state.accounts = state.accounts.filter(account => account.id !== id);
    delete state.connectionStatus[id];
    delete state.syncStatus[id];
    delete state.reports[id];
  },
  
  clearAccountData: (state) => {
    state.accounts = [];
    state.connectionStatus = {};
    state.lastSynced = null;
    state.syncStatus = {};
    state.reports = {};
    state.importHistory = [];
    state.selectedItem = null;
    state.error = null;
  },
});

// Add extra reducers for RTK Query
sageAccountSlice.caseReducers = {
  ...sageAccountSlice.caseReducers,
  extraReducers: (builder) => {
    // Handle accounts fetch success
    builder.addMatcher(
      sageAccountApi.endpoints.getSageAccounts.matchFulfilled,
      (state, action) => {
        state.accounts = action.payload?.data || action.payload || [];
        state.error = null;
      }
    );

    // Handle account fetch by ID success
    builder.addMatcher(
      sageAccountApi.endpoints.getSageAccountById.matchFulfilled,
      (state, action) => {
        state.selectedItem = action.payload;
        state.error = null;
      }
    );

    // Handle account create success
    builder.addMatcher(
      sageAccountApi.endpoints.createSageAccount.matchFulfilled,
      (state, action) => {
        state.accounts.push(action.payload);
        state.error = null;
      }
    );

    // Handle account update success
    builder.addMatcher(
      sageAccountApi.endpoints.updateSageAccount.matchFulfilled,
      (state, action) => {
        const updatedAccount = action.payload;
        const index = state.accounts.findIndex(account => account.id === updatedAccount.id);
        if (index !== -1) {
          state.accounts[index] = updatedAccount;
        }
        state.error = null;
      }
    );

    // Handle account status update success
    builder.addMatcher(
      sageAccountApi.endpoints.updateSageAccountStatus.matchFulfilled,
      (state, action) => {
        const updatedAccount = action.payload;
        const index = state.accounts.findIndex(account => account.id === updatedAccount.id);
        if (index !== -1) {
          state.accounts[index] = { ...state.accounts[index], ...updatedAccount };
        }
        state.error = null;
      }
    );

    // Handle account sync success
    builder.addMatcher(
      sageAccountApi.endpoints.syncSageAccount.matchFulfilled,
      (state, action) => {
        const syncedAccount = action.payload;
        const index = state.accounts.findIndex(account => account.id === syncedAccount.id);
        if (index !== -1) {
          state.accounts[index] = { ...state.accounts[index], ...syncedAccount };
        }
        state.lastSynced = new Date().toISOString();
        state.syncStatus[syncedAccount.id] = 'completed';
        state.error = null;
      }
    );

    // Handle account delete success
    builder.addMatcher(
      sageAccountApi.endpoints.deleteSageAccount.matchFulfilled,
      (state, action) => {
        const deletedId = action.meta.arg.originalArgs || action.meta.arg;
        state.accounts = state.accounts.filter(account => account.id !== deletedId);
        delete state.connectionStatus[deletedId];
        delete state.syncStatus[deletedId];
        delete state.reports[deletedId];
        state.error = null;
      }
    );

    // Handle connection status fetch success
    builder.addMatcher(
      sageAccountApi.endpoints.getSageConnectionStatus.matchFulfilled,
      (state, action) => {
        const accountId = action.meta.arg.originalArgs || action.meta.arg;
        state.connectionStatus[accountId] = action.payload;
        state.error = null;
      }
    );

    // Handle connection test success
    builder.addMatcher(
      sageAccountApi.endpoints.testSageConnection.matchFulfilled,
      (state, action) => {
        const accountId = action.meta.arg.originalArgs || action.meta.arg;
        state.connectionStatus[accountId] = action.payload;
        state.error = null;
      }
    );

    // Handle reports fetch success
    builder.addMatcher(
      sageAccountApi.endpoints.getSageReports.matchFulfilled,
      (state, action) => {
        const { accountId, reportType } = action.meta.arg.originalArgs || action.meta.arg;
        if (!state.reports[accountId]) {
          state.reports[accountId] = {};
        }
        state.reports[accountId][reportType] = action.payload;
        state.error = null;
      }
    );

    // Handle data fetch success
    builder.addMatcher(
      sageAccountApi.endpoints.fetchSageData.matchFulfilled,
      (state, action) => {
        // Store fetched data
        state.data = action.payload;
        state.error = null;
      }
    );

    // Handle import success
    builder.addMatcher(
      sageAccountApi.endpoints.importSageData.matchFulfilled,
      (state, action) => {
        // Add to import history
        state.importHistory.unshift({
          id: Date.now(),
          timestamp: new Date().toISOString(),
          status: 'success',
          data: action.payload,
        });
        // Keep only last 50 records
        if (state.importHistory.length > 50) {
          state.importHistory = state.importHistory.slice(0, 50);
        }
        state.error = null;
      }
    );

    // Handle any Sage API errors
    builder.addMatcher(
      (action) => action.type.endsWith('/rejected') && action.type.includes('sage'),
      (state, action) => {
        state.error = action.payload?.message || 'Sage operation failed';
        
        // Add failed import to history if it was an import operation
        if (action.type.includes('import')) {
          state.importHistory.unshift({
            id: Date.now(),
            timestamp: new Date().toISOString(),
            status: 'failed',
            error: state.error,
          });
        }
      }
    );
  },
};

export const {
  setConnectionStatus,
  setSyncStatus,
  setLastSynced,
  setReports,
  addImportHistory,
  updateAccountInList,
  addAccountToList,
  removeAccountFromList,
  clearAccountData,
  setLoading,
  setError,
  clearError,
  setItems,
  setSelectedItem,
  clearSelectedItem,
  setFilters,
  resetFilters,
  setPagination,
} = sageAccountSlice.actions;

export default sageAccountSlice.reducer;
