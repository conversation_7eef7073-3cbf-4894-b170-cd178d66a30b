import { api } from '../api';

// Login API endpoints using RTK Query
export const loginApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Login user
    login: builder.mutation({
      query: (credentials) => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Auth'],
      transformResponse: (response) => {
        // Store token in localStorage for persistence
        if (response.data?.token) {
          localStorage.setItem('token', response.data.token);
        }
        return response;
      },
    }),

    // Logout user
    logout: builder.mutation({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
      invalidatesTags: ['Auth'],
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
          // Clear token from localStorage
          localStorage.removeItem('token');
          // Clear auth state
          dispatch(api.util.resetApiState());
        } catch (error) {
          // Even if logout fails on server, clear local data
          localStorage.removeItem('token');
          dispatch(api.util.resetApiState());
        }
      },
    }),

    // Refresh token
    refreshToken: builder.mutation({
      query: () => ({
        url: '/auth/refresh-token',
        method: 'POST',
      }),
      transformResponse: (response) => {
        // Update token in localStorage
        if (response.data?.token) {
          localStorage.setItem('token', response.data.token);
        }
        return response;
      },
    }),

    // Sign up user
    signUp: builder.mutation({
      query: (userData) => ({
        url: '/auth/signup',
        method: 'POST',
        body: userData,
      }),
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useLoginMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  useSignUpMutation,
} = loginApi;
