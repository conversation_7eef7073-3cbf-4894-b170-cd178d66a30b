import { combineReducers } from '@reduxjs/toolkit';

// Import accounts sub-slices
import quickbookAccountReducer from '../slices/accounts/quickbookAccountSlice';
import netsuiteAccountReducer from '../slices/accounts/netsuiteAccountSlice';
import sageAccountReducer from '../slices/accounts/sageAccountSlice';

// Import accounts thunks for extraReducers
import quickbookAccountThunks from '../thunks/accounts/quickbookAccountThunk';

// Combine all accounts-related reducers
const accountsReducer = combineReducers({
  quickbook: quickbookAccountReducer,
  netsuite: netsuiteAccountReducer,
  sage: sageAccountReducer,
});

// Create a wrapper to add extra reducers for thunks
const accountsReducerWithThunks = (state, action) => {
  // First, let the combined reducer handle the action
  const newState = accountsReducer(state, action);
  
  // Then handle thunk actions that affect multiple sub-slices
  switch (action.type) {
    // Handle bulk sync QuickBooks accounts success
    case quickbookAccountThunks.bulkSyncQuickbookAccounts.fulfilled.type:
      const { results } = action.payload;
      let updatedAccounts = [...newState.quickbook.accounts];
      
      results.forEach(result => {
        const index = updatedAccounts.findIndex(account => account.id === result.id);
        if (index !== -1) {
          updatedAccounts[index] = { ...updatedAccounts[index], ...result };
        }
      });
      
      return {
        ...newState,
        quickbook: {
          ...newState.quickbook,
          accounts: updatedAccounts,
          lastSynced: new Date().toISOString(),
          error: null,
        },
      };
    
    // Handle comprehensive report generation success
    case quickbookAccountThunks.generateComprehensiveReport.fulfilled.type:
      const { accountId, reportData } = action.payload;
      return {
        ...newState,
        quickbook: {
          ...newState.quickbook,
          reports: {
            ...newState.quickbook.reports,
            [accountId]: {
              ...newState.quickbook.reports[accountId],
              comprehensive: reportData,
            },
          },
          error: null,
        },
      };
    
    // Handle reconciliation success
    case quickbookAccountThunks.reconcileQuickbookData.fulfilled.type:
      const reconciledAccount = action.payload;
      const reconciledIndex = newState.quickbook.accounts.findIndex(
        account => account.id === reconciledAccount.id
      );
      let reconciledAccounts = [...newState.quickbook.accounts];
      
      if (reconciledIndex !== -1) {
        reconciledAccounts[reconciledIndex] = {
          ...reconciledAccounts[reconciledIndex],
          ...reconciledAccount,
          lastReconciled: new Date().toISOString(),
        };
      }
      
      return {
        ...newState,
        quickbook: {
          ...newState.quickbook,
          accounts: reconciledAccounts,
          error: null,
        },
      };
    
    // Handle backup success
    case quickbookAccountThunks.backupQuickbookData.fulfilled.type:
      const backupResult = action.payload;
      const backupAccountIndex = newState.quickbook.accounts.findIndex(
        account => account.id === backupResult.accountId
      );
      let backupAccounts = [...newState.quickbook.accounts];
      
      if (backupAccountIndex !== -1) {
        backupAccounts[backupAccountIndex] = {
          ...backupAccounts[backupAccountIndex],
          lastBackup: backupResult.timestamp,
          backupStatus: 'completed',
        };
      }
      
      return {
        ...newState,
        quickbook: {
          ...newState.quickbook,
          accounts: backupAccounts,
          error: null,
        },
      };
    
    // Handle restore success
    case quickbookAccountThunks.restoreQuickbookData.fulfilled.type:
      const restoreResult = action.payload;
      const restoreAccountIndex = newState.quickbook.accounts.findIndex(
        account => account.id === restoreResult.accountId
      );
      let restoreAccounts = [...newState.quickbook.accounts];
      
      if (restoreAccountIndex !== -1) {
        restoreAccounts[restoreAccountIndex] = {
          ...restoreAccounts[restoreAccountIndex],
          lastRestore: restoreResult.timestamp,
          restoreStatus: 'completed',
        };
      }
      
      return {
        ...newState,
        quickbook: {
          ...newState.quickbook,
          accounts: restoreAccounts,
          error: null,
        },
      };
    
    // Handle connection validation success
    case quickbookAccountThunks.validateQuickbookConnection.fulfilled.type:
      const validationResult = action.payload;
      const validationAccountIndex = newState.quickbook.accounts.findIndex(
        account => account.id === validationResult.accountId
      );
      let validationAccounts = [...newState.quickbook.accounts];
      
      if (validationAccountIndex !== -1) {
        validationAccounts[validationAccountIndex] = {
          ...validationAccounts[validationAccountIndex],
          connectionStatus: validationResult.status,
          lastValidated: new Date().toISOString(),
        };
      }
      
      return {
        ...newState,
        quickbook: {
          ...newState.quickbook,
          accounts: validationAccounts,
          error: null,
        },
      };
    
    // Handle token refresh success
    case quickbookAccountThunks.refreshQuickbookToken.fulfilled.type:
      const tokenResult = action.payload;
      const tokenAccountIndex = newState.quickbook.accounts.findIndex(
        account => account.id === tokenResult.accountId
      );
      let tokenAccounts = [...newState.quickbook.accounts];
      
      if (tokenAccountIndex !== -1) {
        tokenAccounts[tokenAccountIndex] = {
          ...tokenAccounts[tokenAccountIndex],
          token: tokenResult.token,
          tokenExpiry: tokenResult.expiry,
          lastTokenRefresh: new Date().toISOString(),
        };
      }
      
      return {
        ...newState,
        quickbook: {
          ...newState.quickbook,
          accounts: tokenAccounts,
          error: null,
        },
      };
    
    // Handle schedule sync success
    case quickbookAccountThunks.scheduleQuickbookSync.fulfilled.type:
      const scheduleResult = action.payload;
      const scheduleAccountIndex = newState.quickbook.accounts.findIndex(
        account => account.id === scheduleResult.accountId
      );
      let scheduleAccounts = [...newState.quickbook.accounts];
      
      if (scheduleAccountIndex !== -1) {
        scheduleAccounts[scheduleAccountIndex] = {
          ...scheduleAccounts[scheduleAccountIndex],
          syncSchedule: scheduleResult.schedule,
          scheduledSyncEnabled: true,
        };
      }
      
      return {
        ...newState,
        quickbook: {
          ...newState.quickbook,
          accounts: scheduleAccounts,
          error: null,
        },
      };
    
    // Handle accounts errors
    case quickbookAccountThunks.bulkSyncQuickbookAccounts.rejected.type:
    case quickbookAccountThunks.generateComprehensiveReport.rejected.type:
    case quickbookAccountThunks.reconcileQuickbookData.rejected.type:
    case quickbookAccountThunks.backupQuickbookData.rejected.type:
    case quickbookAccountThunks.restoreQuickbookData.rejected.type:
    case quickbookAccountThunks.validateQuickbookConnection.rejected.type:
    case quickbookAccountThunks.refreshQuickbookToken.rejected.type:
    case quickbookAccountThunks.scheduleQuickbookSync.rejected.type:
      return {
        ...newState,
        quickbook: {
          ...newState.quickbook,
          error: action.payload,
          loading: false,
        },
      };
    
    default:
      return newState;
  }
};

// Export selectors for the accounts module
export const accountsSelectors = {
  // QuickBooks selectors
  selectQuickbookAccounts: (state) => state.accounts.quickbook.accounts,
  selectQuickbookOAuthUrl: (state) => state.accounts.quickbook.oauthUrl,
  selectQuickbookLastSynced: (state) => state.accounts.quickbook.lastSynced,
  selectQuickbookSyncStatus: (state) => state.accounts.quickbook.syncStatus,
  selectQuickbookReports: (state) => state.accounts.quickbook.reports,
  selectSelectedQuickbookAccount: (state) => state.accounts.quickbook.selectedItem,
  selectQuickbookLoading: (state) => state.accounts.quickbook.loading,
  selectQuickbookError: (state) => state.accounts.quickbook.error,
  
  // NetSuite selectors
  selectNetsuiteAccounts: (state) => state.accounts.netsuite.accounts,
  selectNetsuiteConnectionStatus: (state) => state.accounts.netsuite.connectionStatus,
  selectNetsuiteLastSynced: (state) => state.accounts.netsuite.lastSynced,
  selectNetsuiteSyncStatus: (state) => state.accounts.netsuite.syncStatus,
  selectNetsuiteReports: (state) => state.accounts.netsuite.reports,
  selectSelectedNetsuiteAccount: (state) => state.accounts.netsuite.selectedItem,
  selectNetsuiteLoading: (state) => state.accounts.netsuite.loading,
  selectNetsuiteError: (state) => state.accounts.netsuite.error,
  
  // Sage selectors
  selectSageAccounts: (state) => state.accounts.sage.accounts,
  selectSageConnectionStatus: (state) => state.accounts.sage.connectionStatus,
  selectSageLastSynced: (state) => state.accounts.sage.lastSynced,
  selectSageSyncStatus: (state) => state.accounts.sage.syncStatus,
  selectSageReports: (state) => state.accounts.sage.reports,
  selectSageImportHistory: (state) => state.accounts.sage.importHistory,
  selectSelectedSageAccount: (state) => state.accounts.sage.selectedItem,
  selectSageLoading: (state) => state.accounts.sage.loading,
  selectSageError: (state) => state.accounts.sage.error,
  
  // Combined selectors
  selectAccountsState: (state) => state.accounts,
  selectAllAccountsLoading: (state) => 
    state.accounts.quickbook.loading || 
    state.accounts.netsuite.loading || 
    state.accounts.sage.loading,
  selectAnyAccountsError: (state) => 
    state.accounts.quickbook.error || 
    state.accounts.netsuite.error || 
    state.accounts.sage.error,
  
  // Helper selectors
  selectQuickbookAccountById: (state, accountId) => 
    state.accounts.quickbook.accounts.find(account => account.id === accountId),
  selectNetsuiteAccountById: (state, accountId) => 
    state.accounts.netsuite.accounts.find(account => account.id === accountId),
  selectSageAccountById: (state, accountId) => 
    state.accounts.sage.accounts.find(account => account.id === accountId),
  selectQuickbookSyncStatusById: (state, accountId) => 
    state.accounts.quickbook.syncStatus[accountId],
  selectNetsuiteConnectionStatusById: (state, accountId) => 
    state.accounts.netsuite.connectionStatus[accountId],
  selectSageConnectionStatusById: (state, accountId) => 
    state.accounts.sage.connectionStatus[accountId],
};

export default accountsReducerWithThunks;
