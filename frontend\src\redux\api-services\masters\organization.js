import { api } from "../api";

// Organization API endpoints using RTK Query
export const organizationApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all organizations
    getOrganizations: builder.query({
      query: (params = {}) => ({
        url: "/organization",
        params: {
          page: params.page || 1,
          pageSize: params.pageSize || 10,
          ...params,
        },
      }),
      providesTags: ["Organization"],
      transformResponse: (response) => response.data,
    }),

    // Get organization by ID
    getOrganizationById: builder.query({
      query: (id) => `/organization/${id}`,
      providesTags: (result, error, id) => [{ type: "Organization", id }],
      transformResponse: (response) => response.data,
    }),

    // Create organization
    createOrganization: builder.mutation({
      query: (organizationData) => ({
        url: "/organization",
        method: "POST",
        body: organizationData,
      }),
      invalidatesTags: ["Organization"],
      transformResponse: (response) => response.data,
    }),

    // Update organization
    updateOrganization: builder.mutation({
      query: ({ id, ...organizationData }) => ({
        url: `/organization/${id}`,
        method: "PUT",
        body: organizationData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Organization", id },
        "Organization",
      ],
      transformResponse: (response) => response.data,
    }),

    // Delete organization (soft delete)
    deleteOrganization: builder.mutation({
      query: (id) => ({
        url: `/organization/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Organization"],
      transformResponse: (response) => response.data,
    }),

    // Get organization settings
    getOrganizationSettings: builder.query({
      query: (orgId) => `/organization/${orgId}/settings`,
      providesTags: (result, error, orgId) => [
        { type: "Organization", id: `${orgId}-settings` },
      ],
      transformResponse: (response) => response.data,
    }),

    // Update organization settings
    updateOrganizationSettings: builder.mutation({
      query: ({ orgId, settings }) => ({
        url: `/organization/${orgId}/settings`,
        method: "PUT",
        body: settings,
      }),
      invalidatesTags: (result, error, { orgId }) => [
        { type: "Organization", id: `${orgId}-settings` },
        { type: "Organization", id: orgId },
      ],
      transformResponse: (response) => response.data,
    }),

    // Get organization users
    getOrganizationUsers: builder.query({
      query: (orgId) => `/organization/${orgId}/users`,
      providesTags: (result, error, orgId) => [
        { type: "Organization", id: `${orgId}-users` },
      ],
      transformResponse: (response) => response.data,
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useGetOrganizationsQuery,
  useGetOrganizationByIdQuery,
  useCreateOrganizationMutation,
  useUpdateOrganizationMutation,
  useDeleteOrganizationMutation,
  useGetOrganizationSettingsQuery,
  useUpdateOrganizationSettingsMutation,
  useGetOrganizationUsersQuery,
} = organizationApi;
