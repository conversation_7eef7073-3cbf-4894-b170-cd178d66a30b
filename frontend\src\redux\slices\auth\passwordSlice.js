import { createStandardSlice } from '../../../utils/methods/redux';
import { passwordApi } from '../../api-services/auth/password';

const initialState = {
  resetToken: null,
  resetEmail: null,
  passwordStrength: 0,
  lastPasswordChange: null,
  forgotPasswordSent: false,
};

const passwordSlice = createStandardSlice('password', initialState, {
  // Custom reducers specific to password management
  setResetToken: (state, action) => {
    state.resetToken = action.payload;
  },
  
  setResetEmail: (state, action) => {
    state.resetEmail = action.payload;
  },
  
  setPasswordStrength: (state, action) => {
    state.passwordStrength = action.payload;
  },
  
  setLastPasswordChange: (state, action) => {
    state.lastPasswordChange = action.payload;
  },
  
  setForgotPasswordSent: (state, action) => {
    state.forgotPasswordSent = action.payload;
  },
  
  clearPasswordState: (state) => {
    state.resetToken = null;
    state.resetEmail = null;
    state.passwordStrength = 0;
    state.forgotPasswordSent = false;
    state.error = null;
  },
});

// Add extra reducers for RTK Query
passwordSlice.caseReducers = {
  ...passwordSlice.caseReducers,
  extraReducers: (builder) => {
    // Handle forgot password success
    builder.addMatcher(
      passwordApi.endpoints.forgotPassword.matchFulfilled,
      (state, action) => {
        state.forgotPasswordSent = true;
        state.resetEmail = action.meta.arg.email;
        state.error = null;
      }
    );

    // Handle forgot password error
    builder.addMatcher(
      passwordApi.endpoints.forgotPassword.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || 'Failed to send reset email';
        state.forgotPasswordSent = false;
      }
    );

    // Handle reset password success
    builder.addMatcher(
      passwordApi.endpoints.resetPassword.matchFulfilled,
      (state, action) => {
        state.lastPasswordChange = new Date().toISOString();
        state.resetToken = null;
        state.resetEmail = null;
        state.forgotPasswordSent = false;
        state.error = null;
      }
    );

    // Handle reset password error
    builder.addMatcher(
      passwordApi.endpoints.resetPassword.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || 'Failed to reset password';
      }
    );

    // Handle change password success
    builder.addMatcher(
      passwordApi.endpoints.changePassword.matchFulfilled,
      (state, action) => {
        state.lastPasswordChange = new Date().toISOString();
        state.error = null;
      }
    );

    // Handle change password error
    builder.addMatcher(
      passwordApi.endpoints.changePassword.matchRejected,
      (state, action) => {
        state.error = action.payload?.message || 'Failed to change password';
      }
    );
  },
};

export const {
  setResetToken,
  setResetEmail,
  setPasswordStrength,
  setLastPasswordChange,
  setForgotPasswordSent,
  clearPasswordState,
  setLoading,
  setError,
  clearError,
} = passwordSlice.actions;

export default passwordSlice.reducer;
